CREATE DATABASE /*!32312 IF NOT EXISTS*/`balte_oms` /*!40100 DEFAULT CHARACTER SET latin1 */;

USE `balte_oms`;

GRANT ALL PRIVILEGES ON airflow.* TO 'airflow'@'%';

CREATE TABLE mcx_test (
    `TRADEID` INT(11) DEFAULT NULL,
    `SY<PERSON>OL` VARCHAR(15) DEFAULT NULL,
    `QUANTITY` INT(11) DEFAULT NULL,
    `STRATEGY` VARCHAR(60) DEFAULT NULL,
    `ENTRY_TIMESTAMP`  DATETIME DEFAULT NULL,
    `SEGMENT` VARCHAR(15) DEFAULT NULL,
    `EXPIRY` VARCHAR(30) DEFAULT NULL,
    `TYPE` VARCHAR(15) DEFAULT NULL,
    `STRIKE` FLOAT DEFAULT NULL,
    `ENTRY_PRICE` FLOAT DEFAULT NULL,
    `ORDER_TYPE` VARCHAR(15) DEFAULT NULL,
    `ENTRY_TIMESTAMP_META`  DATETIME DEFAULT NULL,
    `SLAVE_NAME` VARCHAR(255) DEFAULT NULL,
    `EXTENDED_INFO` VARCHAR(1023)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


CREATE TABLE dead_trade_mcx_test (
    `TRADEID` int(11) DEFAULT NULL,
    `SYMBOL` varchar(15) DEFAULT NULL,
    `QUANTITY` int(11) DEFAULT NULL,
    `STRATEGY` varchar(60) DEFAULT NULL,
    `ENTRY_TIMESTAMP` datetime DEFAULT NULL,
    `SEGMENT` varchar(15) DEFAULT NULL,
    `EXPIRY` varchar(15) DEFAULT NULL,
    `TYPE` varchar(15) DEFAULT NULL,
    `STRIKE` float DEFAULT NULL,
    `ENTRY_PRICE` float DEFAULT NULL,
    `ORDER_TYPE` varchar(15) DEFAULT NULL,
    `EXIT_TIMESTAMP` datetime DEFAULT NULL,
    `EXIT_PRICE` float DEFAULT NULL,
    `ENTRY_TIMESTAMP_META` datetime DEFAULT NULL,
    `EXIT_TIMESTAMP_META` datetime DEFAULT NULL,
    `SLAVE_NAME` varchar(255) DEFAULT NULL,
    `EXTENDED_INFO` varchar(1023) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


CREATE TABLE dead_trade_mcx_test_total
(
    `TRADEID` int(11) DEFAULT NULL,
    `SYMBOL` varchar(15) DEFAULT NULL,
    `QUANTITY` int(11) DEFAULT NULL,
    `STRATEGY` varchar(60) DEFAULT NULL,
    `ENTRY_TIMESTAMP` datetime DEFAULT NULL,
    `SEGMENT` varchar(15) DEFAULT NULL,
    `EXPIRY` varchar(15) DEFAULT NULL,
    `TYPE` varchar(15) DEFAULT NULL,
    `STRIKE` float DEFAULT NULL,
    `ENTRY_PRICE` float DEFAULT NULL,
    `ORDER_TYPE` varchar(15) DEFAULT NULL,
    `EXIT_TIMESTAMP` datetime DEFAULT NULL,
    `EXIT_PRICE` float DEFAULT NULL,
    `ENTRY_TIMESTAMP_META` datetime DEFAULT NULL,
    `EXIT_TIMESTAMP_META` datetime DEFAULT NULL,
    `SLAVE_NAME` varchar(255) DEFAULT NULL,
    `EXTENDED_INFO` varchar(1023) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1; 