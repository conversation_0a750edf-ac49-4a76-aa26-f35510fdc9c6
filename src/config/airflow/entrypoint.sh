#!/bin/bash
set -e
/opt/conda/envs/"${CONDA_ENV_NAME}"/bin/pip install tabulate==0.8.9 aioredis==2.0.1
# Initialize the Airflow database
/opt/conda/envs/"${CONDA_ENV_NAME}"/bin/airflow db init

# Create the Airflow admin user
/opt/conda/envs/"${CONDA_ENV_NAME}"/bin/airflow users create --username admin \
                     --password admin \
                     --firstname Her<PERSON><PERSON> \
                     --lastname Granger \
                     --role Admin \
                     --email <EMAIL>

# Create the Airflow viewer user
/opt/conda/envs/"${CONDA_ENV_NAME}"/bin/airflow users create --username viewer \
                     --password viewer \
                     --firstname Harry \
                     --lastname Potter \
                     --role Viewer \
                     --email <EMAIL>

chmod 4777 -R  ~/airflow/*

# Continue with the CMD command
exec "$@"