from typing import Optional
import asyncio
from aiokafka import AIOKafkaConsumer  # type: ignore
import logging
from toti.rpc import oms_pb2
from core.helpers.configstore import (
    EXECUTION_LEVEL,
    EXCHANGE_TYPE,
    KAFKA_HOST,
    KAFKA_PORT,
)
from core.helpers.one_second_exit_application_components.position_manager import (
    OneSecondExitApplicationPositionManager,
)
from core.helpers.one_second_exit_application_components.structs import (
    OneSecondExitPosition,
)


class KafkaTradeProcessor:
    """
    KafkaTradeProcessor class is responsible for reading msgs from kafka queue and building current positions.

    This class is responsible for consuming messages from Kafka, processing trade-related
        events, and updating the position manager accordingly.

    Attributes:
        consumer (Optional[AIOKafkaConsumer]):
            Kafka consumer instance, initialized on startup and used for message consumption.
        position_manager (OneSecondExitApplicationPositionManager):
            Handles trade positions and updates them in real time.
        asyncio_sleep (float):
            Time in seconds to wait before processing the next Kafka message (default: 0.001).
        is_market_closed (asyncio.Future):
            Flag to indicate if market is closed. Defaults to False.
        one_second_exit_application_logger (logging.Logger):
            Logger instance for debugging and error tracking.
    """

    def __init__(
        self,
        position_manager: OneSecondExitApplicationPositionManager,
        logger: logging.Logger,
    ) -> None:
        """
        Initializes an instance of KafkaTradeProcessor.

        Args:
            position_manager (OneSecondExitApplicationPositionManager):
                Manages active trade positions and updates them based on incoming messages.
            logger (logging.Logger):
                Logger instance for recording events, errors, and debugging information.
        """
        self.consumer: Optional[AIOKafkaConsumer] = None
        self.position_manager: OneSecondExitApplicationPositionManager = (
            position_manager
        )
        self.asyncio_sleep: float = 0.001
        self.is_market_closed: asyncio.Future = asyncio.Future()  # type: ignore
        self.one_second_exit_application_logger: logging.Logger = logger

    def process_kafka_message(self, kafka_message: oms_pb2.LogMessage) -> None:
        """
        Processes an incoming Kafka message and updates the position manager accordingly.

        Args:
            kafka_message (oms_pb2.LogMessage): The Kafka message received, which contains trade details.

        This method:
        - Adds a new position if the message type is ORDER_ENTRY.
        - Removes a position if the message type is ORDER_EXIT.

        Raises:
            Exception: Any unexpected issue during message processing will be logged.
        """
        try:
            log_type: oms_pb2.LogType.ValueType = kafka_message.logType
            if log_type == oms_pb2.LogType.ORDER_ENTRY:
                one_second_exit_position: OneSecondExitPosition = (
                    OneSecondExitPosition.initialize_from_kafka_message(
                        kafka_trade_message=kafka_message.entryRequest,
                    )
                )
                self.position_manager.add_position(
                    one_second_exit_position=one_second_exit_position
                )
                self.one_second_exit_application_logger.info(
                    f"ORDER_ENTRY processed: TradeID {one_second_exit_position.TRADEID}"
                )
            elif log_type == oms_pb2.LogType.ORDER_EXIT:
                trade_id: int = kafka_message.exitRequest.trade_id
                self.position_manager.remove_position(trade_id=trade_id)
                self.one_second_exit_application_logger.info(
                    f"ORDER_EXIT processed: TradeID {trade_id}"
                )
        except Exception as e:
            self.one_second_exit_application_logger.exception(
                f"Error while processing message from kafka - {repr(e)}"
            )
            raise

    async def create_kafka_consumer(self) -> None:
        """
        Initializes and starts a Kafka consumer to listen for trade messages.

        The consumer:
        - Subscribes to the Kafka topic specific to the execution level and exchange type.
        - Uses 'earliest' offset reset to ensure no messages are missed when starting for the first time.
        - Joins a consumer group for coordinated message consumption.
        - Has auto-commit enabled for automatic offset tracking.

        Raises:
            Exception: If an error occurs while creating or starting the consumer.
        """
        kafka_topic: str = f"{EXECUTION_LEVEL}_{EXCHANGE_TYPE}_async_msgs_oms"
        self.one_second_exit_application_logger.info(
            f"Creating Kafka consumer for topic: {kafka_topic}"
        )

        # Suppress kafka logs
        logging.getLogger("aiokafka.conn").setLevel(logging.CRITICAL)
        logging.getLogger("aiokafka.consumer").setLevel(logging.CRITICAL)
        logging.getLogger("kafka.conn").setLevel(logging.CRITICAL)

        try:
            self.consumer = AIOKafkaConsumer(
                kafka_topic,
                bootstrap_servers=f"{KAFKA_HOST}:{KAFKA_PORT}",
                group_id=f"one_sec_exit_consumer__{EXCHANGE_TYPE}_{EXECUTION_LEVEL}",
                auto_offset_reset="earliest",  # Ensures we start from the earliest available message if no offsets exist
                enable_auto_commit=True,  # Automatically commits offsets for processed messages
                session_timeout_ms=250000,  # Maximum time to detect a consumer failure
                rebalance_timeout_ms=25000,  # Time given for rebalancing partitions in case of a consumer group change
            )
            await self.consumer.start()
            self.one_second_exit_application_logger.info(
                f"Kafka consumer started and listening to topic: {kafka_topic}"
            )
        except Exception as e:
            self.one_second_exit_application_logger.exception(
                f"Failed to start Kafka consumer for topic {kafka_topic}: {repr(e)}"
            )
            raise

    async def read_from_kafka_and_process(self) -> None:
        """
        Continuously reads messages from Kafka and processes them.

        - Creates a Kafka consumer if not already initialized.
        - Fetches and processes messages in batches for efficiency.
        - Stops processing after market close time.
        - Ensures graceful shutdown of the Kafka consumer in case of errors or when exiting.

        Raises:
            Exception: Logs and handles any errors that occur during message processing.
        """
        kafka_message: oms_pb2.LogMessage = oms_pb2.LogMessage()
        await self.create_kafka_consumer()
        assert self.consumer is not None

        self.one_second_exit_application_logger.info(
            "Started consuming messages from Kafka."
        )

        try:
            while not self.is_market_closed.done():
                # Fetch up to 100 messages with a 2-second timeout
                message_batch = await self.consumer.getmany(
                    max_records=100, timeout_ms=2000
                )

                messages = [
                    message
                    for message_list in message_batch.values()
                    for message in message_list
                ]

                if not messages:
                    await asyncio.sleep(self.asyncio_sleep)
                    continue

                # Process each message
                for message in messages:
                    try:
                        kafka_message.ParseFromString(message.value)
                        self.process_kafka_message(kafka_message=kafka_message)
                    except Exception as msg_error:
                        self.one_second_exit_application_logger.error(
                            f"Failed to process Kafka message {kafka_message} due to error: {repr(msg_error)}"
                        )

                    await asyncio.sleep(self.asyncio_sleep)

        except Exception as e:
            self.one_second_exit_application_logger.exception(
                f"Error while reading Kafka messages: {repr(e)}"
            )

        finally:
            if self.consumer:
                self.one_second_exit_application_logger.info("Stopping Kafka consumer.")
                await self.consumer.stop()
                self.one_second_exit_application_logger.info(
                    "Kafka consumer stopped successfully."
                )
