from core.helpers.configstore import (
    EXECUTION_LEVEL,
    EXCHANGE_TYPE,
    MYSQL_HOST,
    MYSQL_USER,
    MYSQL_PASSWORD,
    DB_NAME,
    WEBHOOK,
    LOT_QUANTITY,
)
from core.helpers.alerting import send_message_to_gspace
from core.helpers.utils import extract_balte_id
import pandas as pd
from typing import Optional, Any, Union, List, Tuple, Dict, NamedTuple, no_type_check
import pymysql
import balte.balte_config
from balte.enums import EntryExit
from balte.enums import ExchangeType

from toti import Toti
from toti.rpc import oms_pb2


class TradeQueryMixinBase:
    @staticmethod
    def filter_trades(
        query: str,
        params: Union[List[Any], Tuple[Any], Dict[str, Any], None] = None,
    ) -> pd.DataFrame:
        """connect to mysql db according to credentials in env vars and execute read with `query` and `params`

        Args:
            query (str): read query  to be executed

        Returns:
            pd.DataFrame: result of read query
        """
        if not query.strip().lower().startswith("select"):
            raise ValueError(f"Invalid query: {query}")
        with pymysql.connect(
            host=MYSQL_HOST,
            user=MYSQL_USER,
            password=MYSQL_PASSWORD,
            db=DB_NAME,
            charset="utf8mb4",
        ) as connection:
            return pd.read_sql(query, connection, params=params)

    @classmethod
    def get_all_live_trades(cls) -> pd.DataFrame:
        sql_live = f"SELECT * FROM {EXCHANGE_TYPE}_{EXECUTION_LEVEL}"
        live_trades = cls.filter_trades(
            sql_live,
        )
        live_trades = live_trades.sort_values(["TRADEID"]).reset_index(drop=True)
        return live_trades

    @classmethod
    def get_all_dead_trades(cls) -> pd.DataFrame:
        sql_live = f"SELECT * FROM dead_trade_{EXCHANGE_TYPE}_{EXECUTION_LEVEL}"
        dead_trades = cls.filter_trades(
            sql_live,
        )
        dead_trades = dead_trades.sort_values(["TRADEID"]).reset_index(drop=True)
        return dead_trades

    @classmethod
    def get_mismatch_from_live_sheet(
        cls, live_sheet_raw: Optional[pd.DataFrame] = None
    ) -> pd.DataFrame:
        live_sheet_column_names = [
            "SEGMENT",
            "EXPIRY",
            "SYMBOL",
            "TYPE",
            "STRIKE",
            "TOTAL_QUANTITY",
        ]

        from analytics_api import AnalyticsAPI

        if live_sheet_raw is None:
            api = AnalyticsAPI(mode=EXECUTION_LEVEL)
            live_sheet_raw = api.get_live_sheet(
                exchange=EXCHANGE_TYPE.upper()
            ).reset_index()

        live_sheet = live_sheet_raw.copy()

        balte_oms = cls.get_all_live_trades()
        balte_oms = balte_oms.rename(columns={"QUANTITY": "TOTAL_QUANTITY"})
        balte_oms["TOTAL_QUANTITY"] = balte_oms["TOTAL_QUANTITY"].apply(
            lambda x: round((x / LOT_QUANTITY))
        )
        balte_oms = balte_oms[~balte_oms["STRATEGY"].str.startswith("BaLTE")]
        live_sheet = live_sheet.rename(
            columns={col: col.upper() for col in live_sheet.columns}
        )

        for col in live_sheet_column_names:
            if col not in live_sheet:
                live_sheet[col] = pd.Series(dtype=balte_oms.dtypes[col])

        live_sheet["TOTAL_QUANTITY"] = live_sheet["TOTAL_QUANTITY"].apply(
            lambda x: round((x / LOT_QUANTITY))
        )
        live_sheet["EXPIRY"] = live_sheet["EXPIRY"].apply(
            lambda x: x.strftime("%d-%b-%Y")
        )
        live_sheet_aggregated = (
            live_sheet.groupby(["SEGMENT", "EXPIRY", "SYMBOL", "TYPE", "STRIKE"])
            .agg({"TOTAL_QUANTITY": "sum"})
            .reset_index()
        )
        balte_oms_aggregated = (
            balte_oms.groupby(["SEGMENT", "EXPIRY", "SYMBOL", "TYPE", "STRIKE"])
            .agg({"TOTAL_QUANTITY": "sum"})
            .reset_index()
        )
        merged_df = balte_oms_aggregated.merge(
            live_sheet_aggregated,
            on=["SEGMENT", "EXPIRY", "SYMBOL", "TYPE", "STRIKE"],
            how="outer",
            indicator=True,
            suffixes=("_BALTE_OMS", "_LIVE_SHEET"),
        )
        merged_df = merged_df.drop(["_merge"], axis=1)
        merged_df["TOTAL_QUANTITY_BALTE_OMS"] = merged_df[
            "TOTAL_QUANTITY_BALTE_OMS"
        ].fillna(0)
        merged_df["TOTAL_QUANTITY_LIVE_SHEET"] = merged_df[
            "TOTAL_QUANTITY_LIVE_SHEET"
        ].fillna(0)
        mismatched_df = merged_df[
            merged_df["TOTAL_QUANTITY_BALTE_OMS"]
            != merged_df["TOTAL_QUANTITY_LIVE_SHEET"]
        ]
        return mismatched_df


class OmsOpsMixinBase:
    @classmethod
    def send_exit_trades(
        cls,
        trades_to_exit: pd.DataFrame,
        exit_timestamp_meta: pd.Timestamp,
        toti_obj: Toti,
        username: str,
    ) -> None:
        exit_requests_list = []

        for row in trades_to_exit.itertuples():
            sent_exit_request: oms_pb2.BaLTEExitRequest = cls.send_exit_trade(  # type: ignore
                trade_info=row,
                exit_timestamp_meta=exit_timestamp_meta,
                toti_obj=toti_obj,
            )
            exit_requests_list.append(", ".join(str(sent_exit_request).split("\n")))

        exit_requests = "\n".join(exit_requests_list)
        send_message_to_gspace(
            f"""<users/all>
                exit trades sent by {username}:
                ```{exit_requests}```""",
            WEBHOOK,
        )

    @staticmethod
    @no_type_check
    def send_exit_trade(
        trade_info: NamedTuple, exit_timestamp_meta: pd.Timestamp, toti_obj: Toti
    ) -> oms_pb2.BaLTEExitRequest:
        balte_id = extract_balte_id(trade_info=trade_info)
        balte_exit_request = oms_pb2.BaLTEExitRequest(
            trade_id=int(trade_info.TRADEID),
            balte_id=int(balte_id),
            quantity=float(-1 * trade_info.QUANTITY),
            strategy=str(trade_info.STRATEGY),
            exit_price=float(trade_info.ENTRY_PRICE),
            segment=trade_info.SEGMENT,
            order_type=trade_info.ORDER_TYPE,
            order_price=0,
            trigger_price=0,
            limit_price=0,
            slave_name=trade_info.SLAVE_NAME,
            exit_timestamp_meta=exit_timestamp_meta.strftime("%Y-%m-%d %H:%M:%S"),
            extended_info=trade_info.EXTENDED_INFO,
        )
        try:
            toti_obj.send_exit_order(balte_exit_request)
        except Exception as e:
            raise ValueError(
                f"could not exit this balte_exit_request {balte_exit_request} due to error - {e}"
            )
        return balte_exit_request

    @staticmethod
    def send_exit_for_dead_trade(
        toti_obj: Toti,
        trade_id: Optional[int],
        balte_id: Optional[int],
        quantity: Optional[int],
        buy_sell: Optional[str] = None,
    ) -> oms_pb2.Response:
        balte_exit_trade = oms_pb2.DeadTradeRequest(
            trade_id=trade_id, balte_id=balte_id, quantity=quantity, buy_sell=buy_sell
        )
        trade_id_response = toti_obj.send_exit_for_dead_trade(balte_exit_trade)
        return trade_id_response

    @staticmethod
    def send_restrict_request(
        toti_obj: Toti,
        criteria: str,
        entity: str,
    ) -> None:
        """
        To send restrict / unrestrict requests via toti to oms

        Args:
            toti_obj (Toti): exchange specific toti obj
            exchange_type (ExchangeType): exchange type in [NSE, FX, MCX, KRX]
            criteria (str): criteria of restriction ['strategy','symbol','segment']
            entity (str): value of option to be restricted / un-restricted
        """
        restrict_request = oms_pb2.RestrictRequest(
            criteria=str(criteria.lower()), message=str(entity)
        )
        toti_obj.restrict_query(restrict_request)

    @staticmethod
    def send_unrestrict_request(
        toti_obj: Toti,
        criteria: str,
        entity: str,
    ) -> None:
        """
        To send restrict / unrestrict requests via toti to oms

        Args:
            toti_obj (Toti): exchange specific toti obj
            exchange_type (ExchangeType): exchange type in [NSE, FX, MCX, KRX]
            criteria (str): criteria of restriction ['strategy','symbol','segment']
            entity (str): value of option to be restricted / un-restricted
        """
        restrict_request = oms_pb2.RestrictRequest(
            criteria=str(criteria.lower()), message=str(entity)
        )
        toti_obj.unrestrict_query(restrict_request)
