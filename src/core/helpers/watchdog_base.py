import logging
import pandas as pd
import datetime
from balte.strategy import Strategy
from minio import Minio  # type: ignore
from typing import Dict, List

from core.helpers.configstore import (
    MINIO_END_POINT,
    MINIO_ACCESS_KEY,
    MINIO_SECRET_KEY,
    MINIO_BUCKET_NAME,
)
from core.helpers.utils import (
    get_live_strats,
    load_strats_pyce,
    get_local_date,
    get_local_timestamp,
    extract_timestamp_from_filename,
)

minio_client = Minio(
    MINIO_END_POINT,
    access_key=MINIO_ACCESS_KEY,
    secret_key=MINIO_SECRET_KEY,
    secure=False,
)


class WatchdogBase(Strategy):
    def __init__(self) -> None:
        super(WatchdogBase, self).__init__()
        """
        Add data blocks that will be used for watchdog
        """

    def check_if_data_missing(self) -> None:
        """
        (Optionally) check if data blocks have loaded properly, called during initialize
        """
        pass

    def initialize(self) -> None:
        # Remove older pickles
        self.remove_old_pickles(lookback=1)

        self.check_if_data_missing()

        to_be_live = get_live_strats()
        load_strats_pyce(to_be_live)

        for strat in to_be_live:
            try:
                exec("from " + strat + " import " + strat)
            except Exception as e:
                logging.exception(
                    f" the pyce of the following strat is not present {strat} and received the following exception {repr(e)}"
                )

    def next(self, today: pd.Timestamp, timestamp: pd.Timestamp) -> None:
        # ADD CHECKS IN SUBCLASS

        self._keep_only_30_min_bar_pickles_or_last_10()

    def remove_old_pickles(self, lookback: int = 7) -> None:
        """
        Removes pickles from location 'path' with lookback of 'lookback'
        Args:
            lookback (int, optional): number of days of pickles to leave in the 'path' directory. Defaults to 7.
        """

        deletion_date = self.get_previous_date(
            get_local_date(),
            lookback,
        )
        logging.info(
            f"Module:{self.__class__.__name__} Deleting pickles before {deletion_date} from {MINIO_BUCKET_NAME}"
        )
        deleted_count = 0
        skipped_count = 0
        for file_obj in minio_client.list_objects(MINIO_BUCKET_NAME, "pickles/"):
            file = file_obj.object_name
            name, date, time = extract_timestamp_from_filename(file)
            if date is None:
                raise ValueError(f"Incorrect file name: {file}, does not have date")
            if time is None:
                time = "00-00"
            timestamp = datetime.datetime.strptime(date + "_" + time, "%Y-%m-%d_%H-%M")

            if timestamp < deletion_date:
                minio_client.remove_object(MINIO_BUCKET_NAME, file)
                deleted_count += 1
            else:
                skipped_count += 1
        logging.info(
            f"Module:{self.__class__.__name__} DELETED {deleted_count} NOT DELETED {skipped_count}"
        )

        considered_list = [
            x.object_name
            for x in minio_client.list_objects(MINIO_BUCKET_NAME, "pickles/")
            if f"{deletion_date.strftime('%Y-%m-%d')}" in x.object_name
        ]
        to_delete = [
            x
            for x in considered_list
            if f"{deletion_date.strftime('%Y-%m-%d')}_00-00" not in x
        ]
        for x in to_delete:
            minio_client.remove_object(MINIO_BUCKET_NAME, x)

    def _keep_only_30_min_bar_pickles_or_last_10(self) -> None:
        """
        To save disk space, we can remove pickles so that we have pickles of only 30 min bars intraday and for
        error handling, we have last 30 mins full pickles (1min/5min depending on strategy). This has edge cases where a strategy might not run in the last 10 bars.

        Better is to have last 10 + 30 min bars.
        """
        dicts_of_lists: Dict[str, List[datetime.datetime]] = {}
        for file_obj in minio_client.list_objects(MINIO_BUCKET_NAME, "pickles/"):
            file = file_obj.object_name.split("/")[-1]
            name, date, time = extract_timestamp_from_filename(file)
            if date is None:
                raise ValueError(f"Incorrect file name: {file}, does not have date")
            if (time is None) or (time[-2:] in ["00", "30"]):
                # Ignore 30 mins and 1 hr pickles.
                continue
            if name not in dicts_of_lists:
                dicts_of_lists[name] = []
            timestamp = datetime.datetime.strptime(date + "_" + time, "%Y-%m-%d_%H-%M")
            dicts_of_lists[name].append(timestamp)
        for strat_name, vals in dicts_of_lists.items():
            vals = sorted(vals)[:-10]  # everything except last 10
            for item in vals:
                minio_client.remove_object(
                    MINIO_BUCKET_NAME,
                    "pickles/" + f"{strat_name}_{item.strftime('%Y-%m-%d_%H-%M')}",
                )
        logging.info(
            f"deleted pickles till {get_local_timestamp() - datetime.timedelta(minutes=10)} except 00 and 30 minute bar pickle"
        )
