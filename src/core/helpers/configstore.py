import os

EXCHANGE_TYPE = os.environ["EXCHANGE_TYPE"]
EXECUTION_LEVEL = os.environ["EXECUTION_LEVEL"]
SEGMENT = os.environ["SEGMENT"]
MYSQL_HOST = os.environ["MYSQL_HOST"]
DB_NAME = os.environ["MYSQL_DATABASE"]
MYSQL_USER = os.environ["MYSQL_USER"]
MYSQL_PASSWORD = os.environ["MYSQL_PASSWORD"]
MINIO_ACCESS_KEY = os.environ["MINIO_ACCESS_KEY"]
MINIO_SECRET_KEY = os.environ["MINIO_SECRET_KEY"]
MINIO_BUCKET_NAME = f"{EXCHANGE_TYPE}-{EXECUTION_LEVEL}"
MINIO_END_POINT = os.environ["MINIO_HOST"]
MINIO_STRATEGY_NOT_TO_TRADE_PATH = (
    f"balte_uploads/strategy_restriction_list_{EXCHANGE_TYPE}_{EXECUTION_LEVEL}.csv"
)
MINIO_SEGMENT_NOT_TO_TRADE_PATH = (
    f"balte_uploads/segment_restriction_list_{EXCHANGE_TYPE}_{EXECUTION_LEVEL}.csv"
)
MINIO_SYMBOL_NOT_TO_TRADE_PATH = (
    f"balte_uploads/symbol_restriction_list_{EXCHANGE_TYPE}_{EXECUTION_LEVEL}.csv"
)
KAFKA_HOST = os.environ["KAFKA_HOST"]
KAFKA_PORT = os.environ["KAFKA_PORT"]
OMS_HOST = os.environ["OMS_HOST"]
OMS_PORT = os.environ["OMS_PORT"]
VAULT_URL = os.environ["VAULT_URL"]
WEBHOOK = os.environ["WEBHOOK"]
AIRFLOW_TIMEZONE = os.environ["AIRFLOW_TIMEZONE"]
LOT_QUANTITY = int(os.environ["LOT_QUANTITY"])
EXCHANGE_TYPE_MAPPING = {
    "nse": 1,
    "fx": 2,
    "mcx": 3,
    "ncdex": 4,
    "krx": 5,
    "gift": 6,
    "bse": 7,
    "us": 8,
}
CONDA_ENV_NAME = os.environ["CONDA_ENV_NAME"]
BALTE_ID_STRIKE_MULTIPLIER = int(os.environ["BALTE_ID_STRIKE_MULTIPLIER"])
TRADING_START_HOUR = int(os.environ["TRADING_START_HOUR"])
TRADING_START_MINUTE = int(os.environ["TRADING_START_MINUTE"])
