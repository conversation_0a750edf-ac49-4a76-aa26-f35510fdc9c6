from core.dags.dag_builder_base import DagBuilderBase
from airflow.models import DAG
import pandas as pd
from typing import Dict
from airflow.operators.python import PythonOperator
from core.helpers.configstore import (
    MINIO_END_POINT,
    MINIO_ACCESS_KEY,
    MINIO_SECRET_KEY,
    MINIO_BUCKET_NAME,
    MINIO_STRATEGY_NOT_TO_TRADE_PATH,
    MINIO_SYMBOL_NOT_TO_TRADE_PATH,
    MINIO_SEGMENT_NOT_TO_TRADE_PATH,
    WEBHOOK,
)
from minio import Minio  # type: ignore
import logging
from core.helpers.alerting import send_message_to_gspace


class RestrictionListAlertDagBuilderBase(DagBuilderBase):
    def __init__(self, dag_id: str, schedule_interval: str) -> None:
        super().__init__(
            dag_id=dag_id,
            schedule_interval=schedule_interval,
        )

    @staticmethod
    def load_restriction_list(
        minio_path: str, header: str, minioClient: Minio
    ) -> pd.DataFrame:
        df: pd.DataFrame = pd.DataFrame(columns=[header])
        try:
            df = pd.read_csv(  # type: ignore
                minioClient.get_object(
                    MINIO_BUCKET_NAME,
                    minio_path,
                ),
                header=None,
            )
            df.rename(columns={0: header}, inplace=True)
        except Exception as e:
            logging.error(
                f"Could not load {header} restriciton file list due to error {repr(e)}"
            )
        return df

    def send_restriction_list_to_gspace(self) -> None:
        minioClient: Minio = Minio(
            MINIO_END_POINT,
            access_key=MINIO_ACCESS_KEY,
            secret_key=MINIO_SECRET_KEY,
            secure=False,
        )
        restricton_df_map: Dict[str, pd.DataFrame] = {}
        restricton_df_map["Strategy Restriction List"] = self.load_restriction_list(
            minio_path=MINIO_STRATEGY_NOT_TO_TRADE_PATH,
            header="strategy",
            minioClient=minioClient,
        )
        restricton_df_map["Segment Restriction List"] = self.load_restriction_list(
            minio_path=MINIO_SEGMENT_NOT_TO_TRADE_PATH,
            header="segment",
            minioClient=minioClient,
        )
        restricton_df_map["Symbol Restriction List"] = self.load_restriction_list(
            minio_path=MINIO_SYMBOL_NOT_TO_TRADE_PATH,
            header="symbol",
            minioClient=minioClient,
        )

        tables = ""

        for key in restricton_df_map:
            tables += f"{key.upper()}\n\n"
            table = restricton_df_map[key].to_markdown(tablefmt="github")
            tables += table + "\n\n"

        body = "```" + tables + "```"
        send_message_to_gspace(body, WEBHOOK)

    def build_dag(self) -> DAG:
        dag = DAG(
            dag_id=self.dag_id,
            schedule_interval=self.schedule_interval,
            default_args=self.dag_default_args,
            catchup=self.dag_catchup,
        )
        restriction_list_alert = PythonOperator(
            task_id="send_restriction_list_to_gspace",
            python_callable=self.send_restriction_list_to_gspace,
            dag=dag,
        )
        return dag
