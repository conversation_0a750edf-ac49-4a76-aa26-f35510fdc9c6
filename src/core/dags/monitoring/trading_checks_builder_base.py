"""
Dag for live trading checks

A few checks present
- dead strategy trades present in live.
- pickle of important cluster not made in the last x minutes
- a specific segment trades haven't come in the last x minutes
"""

from core.dags.dag_builder_base import DagBuilderBase
from airflow.models import DAG
from airflow.operators.python import <PERSON>Operator
from core.helpers.configstore import (
    EXCHANGE_TYPE,
    EXECUTION_LEVEL,
    SEGMENT,
    MINIO_END_POINT,
    MINIO_ACCESS_KEY,
    MINIO_SECRET_KEY,
    MINIO_BUCKET_NAME,
    WEBHOOK,
)
from exchange.helpers.mixins import TradeQueryMixin
from core.helpers.alerting import send_message_to_gspace
from core.dags.utils import get_trading_day_task
from core.helpers.utils import (
    get_local_timestamp,
    get_local_time,
    get_strategy_timestamps,
    load_strats_pyce,
)
import time
import pandas as pd
import datetime
from minio import Minio  # type: ignore
from typing import List, Tuple, Set, Callable, Any
import logging
from core.helpers.utils import extract_timestamp_from_filename


class TradingCheckDagBuilderBase(DagBuilderBase, TradeQueryMixin):
    def __init__(
        self,
        dag_id: str,
        schedule_interval: str,
        trading_check_end_time: datetime.time,
        no_trade_check_segments: List[str],
        no_trade_threshold_min: int,
        pickle_state_check_strats: List[str],
        pickle_state_check_threshold_sec: int,
    ) -> None:
        super().__init__(
            dag_id=dag_id,
            schedule_interval=schedule_interval,
        )
        self.trading_check_end_time = trading_check_end_time
        self.no_trade_check_segments = no_trade_check_segments
        self.no_trade_threshold_min = no_trade_threshold_min
        self.pickle_state_check_strats = pickle_state_check_strats
        self.pickle_state_check_threshold_sec = pickle_state_check_threshold_sec
        logging.basicConfig(
            level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
        )

    def live_strats(self) -> List[str]:
        """make db connection to 199 balte oms to fetch live table

        Returns:
            list , dataframe: it  returns live strategy list from live table and whole live table as well
        """

        balte_data = self.get_all_live_trades()
        live_strategies = balte_data["STRATEGY"].unique().tolist()

        live_strategy = []
        for strat in live_strategies:
            if strat[:5] == "BaLTE":
                live_strategy.append(strat[6:])
            else:
                live_strategy.append(strat)
        return live_strategy

    def dead_strats(self) -> List[str]:
        """make db connection to strat db 214 to fetch dead strategy list

        Returns:
            list: returns list of dead strategies
        """
        from analytics_api import AnalyticsAPI

        api = AnalyticsAPI(mode=EXECUTION_LEVEL)
        dead_strategy_list: List[str] = api.get_dead_strategies(
            segment=SEGMENT.split(","), exchange=[EXCHANGE_TYPE.upper()]
        )
        return dead_strategy_list

    def dead_strats_live_check(self) -> None:
        """main function to check if any dead strategy have live trades in balte oms database even after 9:20 timestamp"""
        dead_strat_list = self.dead_strats()
        live_strat_list = self.live_strats()
        for x in live_strat_list:
            if x in dead_strat_list:
                message = f" dead start- {x} is present in live . please check"
                logging.warning(f"dead_strategy is still live for {x}")
                send_message_to_gspace(message, WEBHOOK)
            else:
                logging.info(f"{x} is not dead and so present in live table")

    def today_eod_pickle_check(self, minio_client: Minio) -> None:
        """Checks if EOD pickle files (timestamp 00-00) exist for today for all live strategies."""
        today = get_local_timestamp().date().strftime("%Y-%m-%d")
        missing_eod_pickles = []
        files = minio_client.list_objects(MINIO_BUCKET_NAME, "pickles/")
        found_eod = set()
        live_strategies = self.pickle_state_check_strats

        for file in files:
            name, date, tm = extract_timestamp_from_filename(file.object_name)
            strategy_name = name.split("/")[-1]
            if date is None:
                raise ValueError(
                    f"Incorrect file name: {file.object_name}, does not have date"
                )

            if (
                date == today
                and tm is not None
                and tm == "00-00"
                and strategy_name in live_strategies
            ):
                found_eod.add(strategy_name)

        for strat in live_strategies:
            if strat not in found_eod:
                missing_eod_pickles.append(strat)

        if missing_eod_pickles:
            message = f"Missing EOD pickle for {today} for strategies: {missing_eod_pickles}. Please investigate."
            logging.error(message)
            send_message_to_gspace(message, WEBHOOK)
        else:
            logging.info(f"EOD pickles found for all relevant strategies for {today}.")

    def pickle_existing_state_check(self, minio_client: Minio) -> None:
        """checks last pickle timestamp and its size by passing strategy name and raise error if pickle not formed in last 10 min

        Raises:
            ValueError: if pickle file not getting formed in last 500 seconds
            ValueError: if pickle file is not accessible
            ValueError: if pickle file size is less than 1 KB
        """

        live_strats = self.pickle_state_check_strats
        if not hasattr(self, "_strategy_timestamps_map"):
            load_strats_pyce(live_strats)
            self._strategy_timestamps_map = get_strategy_timestamps(live_strats)
        strategy_timestamps_map = self._strategy_timestamps_map

        files = list(minio_client.list_objects(MINIO_BUCKET_NAME, "pickles/"))
        current_datetime_local = get_local_timestamp()

        for file_name in live_strats:
            try:
                should_check = False
                if file_name not in strategy_timestamps_map:
                    should_check = True
                else:
                    # Determine if a pickle check is currently relevant for this strategy
                    if strategy_timestamps_map[file_name]:
                        for ts in strategy_timestamps_map[file_name]:
                            # Check if any strategy timestamp is within a window before current timestamp
                            time_difference = (
                                current_datetime_local
                                - current_datetime_local.replace(
                                    hour=ts.hour,
                                    minute=ts.minute,
                                    second=0,
                                    microsecond=0,
                                )
                            ).total_seconds()
                            if time_difference < 0:
                                break
                            if time_difference <= self.pickle_state_check_threshold_sec:
                                should_check = True
                                break
                    else:
                        should_check = True

                if not should_check:
                    logging.info(
                        f"Skipping pickle check for '{file_name}' as it's not within the relevant timeframe."
                    )
                    continue

                matching_files = []
                for file in files:
                    name, date, tm = extract_timestamp_from_filename(file.object_name)
                    strategy_name = name.split("/")[-1]
                    if tm is None or tm == "00-00" or strategy_name != file_name:
                        continue
                    matching_files.append(file)

                if len(matching_files) == 0:
                    raise FileNotFoundError(
                        f"Pickle file for strategy '{file_name}' not found in Minio"
                    )
                matching_files.sort(key=lambda x: x.last_modified)
                pickle_obj = matching_files[-1]
                name, date, tm = extract_timestamp_from_filename(pickle_obj.object_name)
                if date is None:
                    raise ValueError(
                        f"Incorrect file name: {pickle_obj.object_name}, does not have date"
                    )
                if tm is None:
                    tm = "00-00"
                timestamp = datetime.datetime.strptime(
                    date + "_" + tm, "%Y-%m-%d_%H-%M"
                )
                logging.info(
                    f"Pickle file '{file_name}' last modified timestamp: {timestamp}"
                )
                if (
                    get_local_timestamp() - timestamp
                ).seconds > self.pickle_state_check_threshold_sec:
                    logging.warning(
                        f"Pickle file '{pickle_obj.object_name}' was last updated more than 9 minutes ago."
                    )
                    send_message_to_gspace(
                        f"{pickle_obj.object_name} was last pickle formed, and was formed more than {self.pickle_state_check_threshold_sec} seconds ago",
                        WEBHOOK,
                    )
                file_size = pickle_obj.size
                logging.info(f"Size of '{pickle_obj.object_name}': {file_size} bytes")
                # checks for pickle size less than 1 KB
                if file_size < 1000:
                    raise ValueError(
                        f"{pickle_obj.object_name} pickle size is below 1KB "
                    )
            except FileNotFoundError as fnf_error:
                send_message_to_gspace(f"Pickle not found error: {fnf_error}", WEBHOOK)
                logging.error(f"File not found error: {fnf_error}")
            except ValueError as ve:
                send_message_to_gspace(f"Value error: {ve}", WEBHOOK)
                logging.error(f"Value error: {ve}")
            except Exception as e:
                logging.exception(f"Unexpected error for '{file_name}': {e}")
                raise RuntimeError(
                    f"An error occurred while processing '{file_name}'"
                ) from e
        print(get_local_timestamp())

    def cluster_slave_trade_qty_check(self) -> None:
        """checks trades quantity in oms db processed by slave vs cluster for each slave strategy"""

        df = self.get_all_live_trades()
        if len(df) == 0:
            return

        df["ORDER_INFO"] = (
            df["SYMBOL"].astype(str)
            + "_"
            + df["EXPIRY"].astype(str)
            + "_"
            + df["TYPE"].astype(str)
            + "_"
            + df["STRIKE"].astype(str)
        )
        df["QUANTITY"] = df["QUANTITY"].abs()

        df_slaves_in_cluster = df[df["STRATEGY"].str[:7] == "cluster"]
        df_slaves_in_cluster = df_slaves_in_cluster[
            df_slaves_in_cluster["SLAVE_NAME"] != "default_slave"
        ]
        if len(df_slaves_in_cluster) == 0:
            return
        df_grouped_slaves_in_cluster = (
            df_slaves_in_cluster.groupby(["SLAVE_NAME", "ORDER_INFO"])["QUANTITY"]
            .sum()
            .reset_index()
        )
        df_grouped_slaves_in_cluster["ORDER_INFO"] = (
            df_grouped_slaves_in_cluster[["SLAVE_NAME", "ORDER_INFO"]]
            .astype(str)
            .agg("_".join, axis=1)
        )

        df_grouped_slaves_in_cluster = df_grouped_slaves_in_cluster.drop(
            columns=["SLAVE_NAME"], axis=1
        )

        df_balte_slaves = df[df["STRATEGY"].str[:5] == "BaLTE"]
        if len(df_balte_slaves):
            df_grouped_balte_slaves = (
                df_balte_slaves.groupby(["STRATEGY", "ORDER_INFO"])["QUANTITY"]
                .sum()
                .reset_index()
            )
            df_grouped_balte_slaves["ORDER_INFO"] = (
                df_grouped_balte_slaves[["STRATEGY", "ORDER_INFO"]]
                .astype(str)
                .agg("_".join, axis=1)
                .str[6:]
            )
            df_grouped_balte_slaves = df_grouped_balte_slaves.drop(
                columns=["STRATEGY"], axis=1
            )
        else:
            df_grouped_balte_slaves = df_balte_slaves[["ORDER_INFO", "QUANTITY"]]

        ##case if no orders present in slaves but orders are in cluster
        order_info_not_in_balte_slaves = set(
            df_grouped_slaves_in_cluster["ORDER_INFO"]
        ) - set(df_grouped_balte_slaves["ORDER_INFO"])

        if len(order_info_not_in_balte_slaves):
            alert_message = f"The following orders in slave are not present but are in cluster :\n{order_info_not_in_balte_slaves}"
            logging.warning(alert_message)
            send_message_to_gspace(alert_message, WEBHOOK)

        ##case if orders present but quantity has issue
        merged_df = pd.merge(
            df_grouped_slaves_in_cluster,
            df_grouped_balte_slaves,
            on="ORDER_INFO",
            suffixes=("_slaves_in_cluster", "_balte_slaves"),
        )
        filtered_df = merged_df[
            merged_df["QUANTITY_slaves_in_cluster"] > merged_df["QUANTITY_balte_slaves"]
        ]

        if len(filtered_df):
            alert_message = f"The following orders in slave have less net quantity in live compared to cluster:\n{filtered_df.set_index('ORDER_INFO').to_dict()}"
            logging.warning(alert_message)
            send_message_to_gspace(alert_message, WEBHOOK)

    def segment_wise_last_trade_check(self) -> None:
        """checks segment wise last trade is present in last 10 min or not , can pass multiple segments in parameter which needs to be checked

        Raises:
            ValueError: if segment last trade is beyond 10 min .
        """
        balte_data = self.get_all_live_trades()
        balte_data = balte_data[~balte_data["STRATEGY"].str.startswith("BaLTE")]
        balte_data_sorted = balte_data[
            balte_data["ENTRY_TIMESTAMP_META"]
            >= (
                get_local_timestamp()
                - pd.to_timedelta(f"{self.no_trade_threshold_min}min")
            )
        ]
        diff = set(self.no_trade_check_segments) - set(
            balte_data_sorted.SEGMENT.unique().tolist()
        )
        if len(diff) > 0:
            logging.warning(f"{diff} segment trades have not came in last 15 min")
            send_message_to_gspace(
                f"{diff} segment trades have not come in last {self.no_trade_threshold_min} min",
                WEBHOOK,
            )
        else:
            logging.info(
                f"all segment are present in last {self.no_trade_threshold_min} min live trades"
            )

    def run_check(
        self, func: Callable[..., Any], iter_sleep: int, func_kwargs: Any = {}
    ) -> None:
        while True:
            if get_local_time() > self.trading_check_end_time:
                logging.info("Day end; exiting")
                break
            func(**func_kwargs)
            time.sleep(iter_sleep)

    def build_dag(self) -> DAG:
        dag = DAG(
            dag_id=self.dag_id,
            schedule_interval=self.schedule_interval,
            default_args=self.dag_default_args,
            catchup=self.dag_catchup,
        )
        trading_day = get_trading_day_task(dag=dag)

        dead_strat_live_trades_checker = PythonOperator(
            task_id="dead_strat_live_trades_checker",
            python_callable=self.run_check,
            op_kwargs={"func": self.dead_strats_live_check, "iter_sleep": 899},
            dag=dag,
        )

        cluster_slave_trade_qty_checker = PythonOperator(
            task_id="cluster_slave_trade_qty_checker",
            python_callable=self.run_check,
            op_kwargs={"func": self.cluster_slave_trade_qty_check, "iter_sleep": 300},
            dag=dag,
        )

        pickle_state_checker = PythonOperator(
            task_id="pickle_state_checker",
            python_callable=self.run_check,
            op_kwargs={
                "func": self.pickle_existing_state_check,
                "iter_sleep": 599,
                "func_kwargs": {
                    "minio_client": Minio(
                        MINIO_END_POINT,
                        access_key=MINIO_ACCESS_KEY,
                        secret_key=MINIO_SECRET_KEY,
                        secure=False,
                    )
                },
            },
            dag=dag,
        )

        segment_wise_trade_checker = PythonOperator(
            task_id="segment_wise_trade_checker",
            python_callable=self.run_check,
            op_kwargs={"func": self.segment_wise_last_trade_check, "iter_sleep": 899},
            dag=dag,
        )

        eod_pickle_checker = PythonOperator(
            task_id="eod_pickle_checker",
            python_callable=self.today_eod_pickle_check,
            op_kwargs={
                "minio_client": Minio(
                    MINIO_END_POINT,
                    access_key=MINIO_ACCESS_KEY,
                    secret_key=MINIO_SECRET_KEY,
                    secure=False,
                )
            },
            dag=dag,
        )

        trading_day >> [
            dead_strat_live_trades_checker,
            pickle_state_checker,
            cluster_slave_trade_qty_checker,
            segment_wise_trade_checker,
        ]
        pickle_state_checker >> eod_pickle_checker

        return dag
