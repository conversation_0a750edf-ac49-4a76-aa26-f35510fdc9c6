from core.dags.dag_builder_base import DagBuilderBase
from airflow.models import DAG
from airflow.operators.python import PythonOperator
from core.helpers.configstore import WEBHOOK
from core.dags.utils import get_trading_day_task
from core.helpers.alerting import send_message_to_gspace
from exchange.helpers.mixins import TradeQueryMixin
from typing import Optional, TYPE_CHECKING

if TYPE_CHECKING:
    import pandas as pd


class PositionMismatchDagBuilderBase(DagBuilderBase, TradeQueryMixin):
    def __init__(self, dag_id: str, schedule_interval: str) -> None:
        super().__init__(
            dag_id=dag_id,
            schedule_interval=schedule_interval,
        )

    def add_position_mismatch_alerts(
        self, live_sheet_raw: Optional["pd.DataFrame"] = None
    ) -> None:
        balte_oms_live_sheet_mismatch_df = self.get_mismatch_from_live_sheet(
            live_sheet_raw
        )

        if not balte_oms_live_sheet_mismatch_df.empty:
            message = "Position mismatch between balte_oms and live_sheet\n\n```"
            message += balte_oms_live_sheet_mismatch_df.to_markdown(
                index=False, tablefmt="github"
            )
            message += "```"
            send_message_to_gspace(
                message,
                WEBHOOK,
            )

    def build_dag(self) -> DAG:
        dag = DAG(
            dag_id=self.dag_id,
            schedule_interval=self.schedule_interval,
            default_args=self.dag_default_args,
            catchup=self.dag_catchup,
        )
        trading_day = get_trading_day_task(dag=dag)

        position_mismatch_check = PythonOperator(
            task_id="position_mismatch_check",
            python_callable=self.add_position_mismatch_alerts,
            dag=dag,
        )

        trading_day >> position_mismatch_check

        return dag
