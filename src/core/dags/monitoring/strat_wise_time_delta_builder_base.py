from core.dags.dag_builder_base import Dag<PERSON>uilderBase
from airflow.models import DAG
import pandas as pd
from airflow.operators.python import PythonOperator
from airflow.operators.email import EmailOperator
from core.helpers.configstore import (
    EXCHANGE_TYPE,
    EXECUTION_LEVEL,
)
from core.dags.utils import get_trading_day_task
from core.helpers.utils import get_local_date
from core.helpers.alerting import send_email
from airflow.utils.trigger_rule import TriggerRule
import datetime
from typing import List


class StratWiseTimeDeltaBuilderBase(DagBuilderBase):
    def __init__(self, dag_id: str, schedule_interval: str) -> None:
        super().__init__(
            dag_id=dag_id,
            schedule_interval=schedule_interval,
        )
        self.dag_default_args["email"] += [
            "<EMAIL>",
            "<EMAIL>",
        ]

    @staticmethod
    def get_slave_log_file_paths() -> List[str]:
        return [f"/opt/balte_live/log/SLAVE/{EXCHANGE_TYPE}_{EXECUTION_LEVEL}_Live.log"]

    def get_strat_df(self) -> None:
        df_list = []
        current_date = get_local_date()
        dates = [current_date]
        log_files = self.get_slave_log_file_paths()
        for log_file in log_files:
            try:
                with open(log_file) as file1:
                    lines = file1.readlines()

                begin_list = {}
                end_list = {}
                for date in dates:
                    begin_list[date] = 0
                    end_list[date] = 0
                for date in dates:
                    strat_list = []
                    for line in lines:
                        if (datetime.datetime.strftime(date, "%Y-%m-%d") in line) and (
                            "CallNext " in line
                        ):
                            if "BEGIN" in line:
                                begin_list[date] += 1
                            elif "END" in line:
                                end_list[date] += 1

                            strat_list.append(line.split(" "))
                        if (datetime.datetime.strftime(date, "%Y-%m-%d") in line) and (
                            "CallNextFAILED" in line
                        ):
                            begin_list[date] -= 1
                            strat_list.pop()
                    if begin_list[date] == end_list[date]:
                        df_list.append(
                            pd.DataFrame(
                                strat_list,
                                columns=[
                                    "logdate",
                                    "logtime",
                                    "info1",
                                    "info2",
                                    "info3",
                                    "begin",
                                    "strategy",
                                    "info4",
                                    "date",
                                    "time",
                                ],
                            ).drop(columns=["info1", "info2", "info3", "info4"])
                        )
                    else:
                        print("begin = ", begin_list[date], date)
                        print("end = ", end_list[date], date)
            except Exception as e:
                print(e)

        if len(df_list) > 0:
            df = pd.concat(df_list)
            df["datetime"] = pd.to_datetime(df["date"] + " " + df["time"])
            df["logdate"] = df["logdate"].apply(pd.Timestamp)
            df["logtime"] = df["logtime"].apply(pd.Timestamp)
            df.sort_values(["strategy", "logdate", "datetime", "logtime"], inplace=True)
            df["tot_sec"] = df["logtime"].diff()
            df = df[df["begin"] == "END"]
            df["tot_sec"] = df["tot_sec"].apply(lambda x: x.total_seconds())
            df = df[df["tot_sec"] > 5]
            df = df.drop(columns=["logtime", "begin", "date", "time"])
            df.sort_values(["tot_sec", "strategy"], ascending=False, inplace=True)
            send_email(
                "Strategies Time delta",
                df.to_html(),
                self.dag_default_args["email"],
                "html",
            )
        else:
            raise ValueError("df_list can only be empty under certain condition")

    def build_dag(self) -> DAG:
        dag = DAG(
            dag_id=self.dag_id,
            schedule_interval=self.schedule_interval,
            default_args=self.dag_default_args,
            catchup=self.dag_catchup,
        )
        trading_day = get_trading_day_task(dag=dag)

        create_timedelta_and_email_task = PythonOperator(
            task_id="create_timedelta_and_email",
            python_callable=self.get_strat_df,
            dag=dag,
        )

        failure_email_task = EmailOperator(
            task_id="failure_email",
            to=self.dag_default_args["email"],
            html_content=""" <h3>Problem in creating strat delta </h3> """,
            subject="Problem in creating strat delta",
            trigger_rule=TriggerRule.ONE_FAILED,
            dag=dag,
        )

        trading_day >> create_timedelta_and_email_task >> failure_email_task

        return dag
