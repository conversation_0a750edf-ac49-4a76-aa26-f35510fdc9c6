from core.dags.dag_builder_base import Dag<PERSON><PERSON>erBase
from airflow.models import DAG
from slippage_analyzer.slippage_analyzer import SlippageAnalyzer
from slippage_analyzer.enums import ExchangeType
from airflow.operators.python import PythonOperator
from core.helpers.configstore import (
    EXCHANGE_TYPE,
    EXECUTION_LEVEL,
)
from core.dags.utils import get_trading_day_task


class TradingSlippageDagBuilderBase(DagBuilderBase):
    def __init__(self, dag_id: str, schedule_interval: str) -> None:
        super().__init__(
            dag_id=dag_id,
            schedule_interval=schedule_interval,
        )

    def run_slippage(self) -> None:
        exchange: str = EXCHANGE_TYPE.upper()
        exchange_type: ExchangeType = ExchangeType(exchange)
        if exchange_type not in list(ExchangeType):
            raise ValueError(f"{exchange} is not a valid exchange")
        obj_analyzer: SlippageAnalyzer = SlippageAnalyzer(exchange=exchange_type)
        obj_analyzer.start_analysis()

    def build_dag(self) -> DAG:
        dag = DAG(
            dag_id=self.dag_id,
            schedule_interval=self.schedule_interval,
            default_args=self.dag_default_args,
            catchup=self.dag_catchup,
        )
        trading_day = get_trading_day_task(dag=dag)

        slippage_task = PythonOperator(
            task_id=f"{EXCHANGE_TYPE}_{EXECUTION_LEVEL}_slippage_analyzer",
            python_callable=self.run_slippage,
            dag=dag,
        )
        trading_day >> slippage_task

        return dag
