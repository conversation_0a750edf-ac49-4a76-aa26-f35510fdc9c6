from core.dags.dag_builder_base import Dag<PERSON>uilderBase
from airflow.models import DAG
from airflow.operators.bash import Bash<PERSON>perator
from core.dags.utils import get_trading_day_task
from core.helpers.configstore import CONDA_ENV_NAME


class TradePickleDbCheckBuilderBase(DagBuilderBase):
    def __init__(self, dag_id: str, schedule_interval: str) -> None:
        super().__init__(
            dag_id=dag_id,
            schedule_interval=schedule_interval,
        )

    def build_dag(self) -> DAG:
        dag = DAG(
            dag_id=self.dag_id,
            schedule_interval=self.schedule_interval,
            default_args=self.dag_default_args,
            catchup=self.dag_catchup,
        )
        trading_day = get_trading_day_task(dag=dag)

        mismatch_trades = BashOperator(
            task_id="mismatch_trades",
            bash_command=f"/opt/conda/envs/{CONDA_ENV_NAME}/bin/python /app/src/exchange/runners/live_trade_pickle_db.py",
            dag=dag,
        )

        trading_day >> mismatch_trades

        return dag
