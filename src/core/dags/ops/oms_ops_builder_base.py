"""
Each trading system has some external oms operations that are required for it to run smoothly.

Depending on exchange and rules, these include
- Corporate actions on trades
- Rollovers
- Pre-expiry exits to minimize risk (we do this for optstk)

Apart from this, some general oms operations include
- DB copy (example : dead_trade_live to dead_trade_live_total)
- oms latency check
"""

from core.dags.dag_builder_base import DagBuilderBase
from typing import Optional, Any
from airflow.models import DAG
from airflow.operators.bash import BashOperator
from airflow.operators.email import EmailOperator
from airflow.utils.trigger_rule import TriggerRule
from core.helpers.configstore import (
    EXCHANGE_TYPE,
    EXECUTION_LEVEL,
    CONDA_ENV_NAME,
)


def build_email_task(dag: DAG) -> EmailOperator:
    return EmailOperator(
        task_id="email_task",
        to=["<EMAIL>", "Balte_alerts.kivicapital.in"],
        html_content=""" <h3>OMS Ops dag stopped </h3> """,
        subject=f"Problem in OMS Ops of {EXCHANGE_TYPE}_{EXECUTION_LEVEL}",
        trigger_rule=TriggerRule.ONE_FAILED,
        dag=dag,
    )


class OMSRolloverDagBuilderBase(DagBuilderBase):
    def __init__(self, dag_id: str, schedule_interval: str) -> None:
        super().__init__(
            dag_id=dag_id,
            schedule_interval=schedule_interval,
        )

    def build_dag(self) -> DAG:
        dag = DAG(
            dag_id=self.dag_id,
            schedule_interval=self.schedule_interval,
            default_args=self.dag_default_args,
            catchup=self.dag_catchup,
        )
        BashOperator(
            task_id="ROLLOVER",
            bash_command=f"/opt/conda/envs/{CONDA_ENV_NAME}/bin/python /app/src/exchange/runners/oms_rollover.py",
            dag=dag,
        ) >> build_email_task(dag)
        return dag


class OMSDBCopyDagBuilderBase(DagBuilderBase):
    def __init__(self, dag_id: str, schedule_interval: str) -> None:
        super().__init__(
            dag_id=dag_id,
            schedule_interval=schedule_interval,
        )

    def build_dag(self) -> DAG:
        dag = DAG(
            dag_id=self.dag_id,
            schedule_interval=self.schedule_interval,
            default_args=self.dag_default_args,
            catchup=self.dag_catchup,
        )
        BashOperator(
            task_id="DB_COPY",
            bash_command=f"/opt/conda/envs/{CONDA_ENV_NAME}/bin/python /app/src/exchange/runners/oms_db_copy.py",
            dag=dag,
        ) >> build_email_task(dag)
        return dag


class OMSLatencyCheckDagBuilderBase(DagBuilderBase):
    def __init__(self, dag_id: str, schedule_interval: str) -> None:
        super().__init__(
            dag_id=dag_id,
            schedule_interval=schedule_interval,
        )

    def build_dag(self) -> DAG:
        dag = DAG(
            dag_id=self.dag_id,
            schedule_interval=self.schedule_interval,
            default_args=self.dag_default_args,
            catchup=self.dag_catchup,
        )
        BashOperator(
            task_id="LATENCY_CHECK",
            bash_command=f"/opt/conda/envs/{CONDA_ENV_NAME}/bin/python /app/src/exchange/runners/oms_latency_check.py",
            dag=dag,
        ) >> build_email_task(dag)
        return dag


class OMSOneSecondExitApplicationDagBuilderBase(DagBuilderBase):
    def __init__(self, dag_id: str, schedule_interval: str) -> None:
        super().__init__(
            dag_id=dag_id,
            schedule_interval=schedule_interval,
        )

    def build_dag(self) -> DAG:
        dag = DAG(
            dag_id=self.dag_id,
            schedule_interval=self.schedule_interval,
            default_args=self.dag_default_args,
            catchup=self.dag_catchup,
        )
        BashOperator(
            task_id="ONE_SECOND_EXIT_APPLICATION",
            bash_command=f"/opt/conda/envs/{CONDA_ENV_NAME}/bin/python /app/src/exchange/runners/one_second_exit_application.py",
            dag=dag,
        ) >> build_email_task(dag)
        return dag
