from core.runners.runner_base import Runner<PERSON>ase
import streamlit as st
import streamlit_authenticator as stauth  # type: ignore
from exchange.helpers.mixins import TradeQueryMixin
from core.helpers.utils import load_yaml
from core.helpers.configstore import (
    EXCHANGE_TYPE,
    EXECUTION_LEVEL,
    OMS_HOST,
    OMS_PORT,
    EXCHANGE_TYPE_MAPPING,
)
from toti import Toti
from balte.enums import ExchangeType
from typing import Dict
from core.apps.exit_portal_components.operations_base import OperationBase
from exchange.apps.exit_portal_components.re_exit_dead_trade_op import (
    ReExitDeadTradeOperation,
)
from exchange.apps.exit_portal_components.position_mismatch_op import (
    PositionMismatchOperation,
)
from exchange.apps.exit_portal_components.move_live_to_dead_op import (
    MoveLiveToDeadOperation,
)
from exchange.apps.exit_portal_components.exit_slaves_main_trade_op import (
    ExitSlavesMainTradesOperation,
)
from exchange.apps.exit_portal_components.exit_positions_op import ExitPositionOperation
from exchange.apps.exit_portal_components.restrict_entry_op import (
    RestrictEntryOperation,
)
from exchange.apps.exit_portal_components.remove_restriction_op import (
    RemoveRestrictionOperation,
)
from exchange.apps.exit_portal_components.block_unblock_old_entry_trades_op import (
    BlockUnblockOldEntryTradesOperation,
)
from exchange.apps.exit_portal_components.delete_strategy_pickles_op import (
    DeleteStrategyPicklesOperation,
)
from typing import Any, Dict


class ExitPortalBase(RunnerBase, TradeQueryMixin):
    def __init__(self) -> None:
        super().__init__()
        # streamlit deletes key for components that are not rendered in a rerun
        # to save state across multi pages app, we can save them in the state of the
        # object as well, since we are re-using the same object in consecutive reruns
        self.state: Dict[str, Any] = {}

        self._message_to_operation_map: Dict[str, OperationBase] = {}
        self.config = load_yaml("/opt/auth.yaml")

    def render_live_positions(self) -> None:
        """
        Function to render live positions in streamlit UI.
        """
        live_position_table_name = f"{EXCHANGE_TYPE}_{EXECUTION_LEVEL}"
        st.write(" SELECTED TABLE : ", str(live_position_table_name))
        balte_oms = self.get_all_live_trades()
        st.write(" current " + live_position_table_name + " positions :-")
        st.write(balte_oms)

    def register_operations(self) -> None:
        """
        Function to register operations that exit portal has to support.
        Expected to call self.add_operation internally.
        """
        self.add_operation(
            msg_name="EXIT POSITIONS",
            operation=ExitPositionOperation(
                toti_obj=self.state["toti_obj"], username=self.state["username"]
            ),
        )
        self.add_operation(
            msg_name="RESTRICT TAKING ENTRY",
            operation=RestrictEntryOperation(
                toti_obj=self.state["toti_obj"], username=self.state["username"]
            ),
        )
        self.add_operation(
            msg_name="REMOVE RESTRICTION",
            operation=RemoveRestrictionOperation(
                toti_obj=self.state["toti_obj"], username=self.state["username"]
            ),
        )
        self.add_operation(
            msg_name="EXIT SLAVES MAIN TRADES",
            operation=ExitSlavesMainTradesOperation(
                toti_obj=self.state["toti_obj"], username=self.state["username"]
            ),
        )
        self.add_operation(
            msg_name="MOVE LIVE TO DEAD",
            operation=MoveLiveToDeadOperation(username=self.state["username"]),
        )
        self.add_operation(
            msg_name="CHECK POSITION MISMATCH BETWEEN BALTE AND LIVE SHEET",
            operation=PositionMismatchOperation(),
        )
        self.add_operation(
            msg_name="RE-EXIT DEAD TRADE",
            operation=ReExitDeadTradeOperation(toti_obj=self.state["toti_obj"]),
        )

        self.add_operation(
            msg_name="BLOCK/UNBLOCK OLD ENTRY TRADES",
            operation=BlockUnblockOldEntryTradesOperation(
                toti_obj=self.state["toti_obj"]
            ),
        )

        self.add_operation(
            msg_name="DELETE STRATEGY PICKLES",
            operation=DeleteStrategyPicklesOperation(),
        )

    def perform_operations(self) -> None:
        """
        Main function to perform operations in exit portal
        """
        if len(self._message_to_operation_map) == 0:
            raise Exception(
                "No operations added. Please use add_operation function to add operations."
            )
        select_option = st.radio(
            label="SELECT FUNCTION :- ",
            options=list(self._message_to_operation_map.keys()),
        )
        if select_option is not None:
            self._message_to_operation_map[select_option].render()

    def add_operation(self, msg_name: str, operation: OperationBase) -> None:
        """
        Adds an operation to exit portal.

        Args:
            msg_name(str): Name to be selected in UI
            operation (OperationBase): Instance of OperationBase which implements render.
        """
        self._message_to_operation_map[msg_name] = operation

    def run(self, *args: Any, **kwargs: Any) -> None:
        """
        Exit portal runner. This authenticates the user and allows them to perform various operations
        """
        st.title(f"EXIT PORTAL {EXCHANGE_TYPE.upper()} {EXECUTION_LEVEL.upper()}!")

        authenticator = stauth.Authenticate(self.config["credentials"])

        authenticator.login()

        if st.session_state["authentication_status"]:
            authenticator.logout()

            self.state["username"] = st.session_state["username"]
            ## LOAD LIVE DB
            Toti.set_production_mode(f"{OMS_HOST}:{OMS_PORT}")
            exchange_type_enum = ExchangeType(
                EXCHANGE_TYPE_MAPPING[EXCHANGE_TYPE.lower()]
            )
            toti_obj = Toti(exchange_type_enum)
            self.state["toti_obj"] = toti_obj
            self._message_to_operation_map = {}
            self.register_operations()
            self.render_live_positions()
            self.perform_operations()
        elif st.session_state["authentication_status"] is False:
            st.error("Username/password is incorrect")
        elif st.session_state["authentication_status"] is None:
            st.warning("Please enter your username and password")
