import streamlit as st
from toti import Toti
from toti.rpc import oms_pb2
from core.apps.exit_portal_components.operations_base import OperationBase
from exchange.helpers.mixins import TradeQueryMixin, OmsOpsMixin
from typing import Any
import pandas as pd


class ReExitDeadTradeOperationBase(OperationBase, TradeQueryMixin, OmsOpsMixin):
    def __init__(self, toti_obj: Toti) -> None:
        super().__init__()
        self.toti_obj = toti_obj

    def generate_balte_id(self, row: "pd.Series[Any]") -> int:
        """
        Returns the balte_id for the given row
        """
        raise NotImplementedError(
            "Please implement generate_balte_id function to use dead trade exit functionality"
        )

    def render(self) -> None:
        if "re_exit_trade_button_clicked" not in st.session_state:
            st.session_state["re_exit_trade_button_clicked"] = False

        # when outer button was called
        def callback() -> None:
            st.session_state.re_exit_trade_button_clicked = True

        dead_trade_table = self.get_all_dead_trades()
        st.write("Current dead trades are as follows :-")
        st.write(dead_trade_table)
        if not dead_trade_table.empty:
            dead_trade_table["ID"] = dead_trade_table.apply(
                self.generate_balte_id, axis=1
            )
        else:
            dead_trade_table["ID"] = pd.Series(dtype=int)
        trade_id = st.text_input("ENTER TRADE ID :- ")
        if (
            st.button("SEND THE EXIT TRADE", on_click=callback)
            or st.session_state.re_exit_trade_button_clicked
        ):
            st.write(
                "The following dead trade will be sent to execution system. Do you still want to proceed?"
            )
            st.write(dead_trade_table[dead_trade_table["TRADEID"] == int(trade_id)])
            if st.button("YES"):
                st.session_state.re_exit_trade_button_clicked = False
                row = dead_trade_table[
                    dead_trade_table["TRADEID"] == int(trade_id)
                ].iloc[0]
                trade_id_response = self.send_exit_for_dead_trade(
                    self.toti_obj, int(trade_id), row["ID"], (-1 * row["QUANTITY"])
                )
                st.write(trade_id_response)
                if trade_id_response == -1:
                    st.error("Dead trade request failed")

                else:
                    st.success(
                        f"Dead trade request was sent successfully for trade id = {trade_id_response}"
                    )
