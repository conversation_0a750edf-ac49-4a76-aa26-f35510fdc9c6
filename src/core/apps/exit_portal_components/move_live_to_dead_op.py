from toti import Toti
from toti.rpc import oms_pb2
from core.apps.exit_portal_components.operations_base import OperationBase
from core.helpers.configstore import (
    EXCHANGE_TYPE,
    EXECUTION_LEVEL,
    MYSQL_HOST,
    DB_NAME,
    MYSQL_USER,
    MYSQL_PASSWORD,
)
from typing import Optional, List
import pymysql
import pandas as pd
import streamlit as st
from exchange.helpers.mixins import TradeQueryMixin
import logging


class MoveLiveToDeadOperationBase(OperationBase, TradeQueryMixin):
    def __init__(self, username: str) -> None:
        super().__init__()
        self.username = username

    def move_live_to_dead(
        self, trade_id_list: Optional[List[str]], strat: Optional[str]
    ) -> str:
        """
        This function will move live trades to dead table using tradeid or strategy name passed into portal

        Args:
            trade_id_list (List[str]): trades which to move from live to respective dead
            strat (str): all trades corresponding to strat will move from live to dead

        Raises:
            e: error if mysql command doesnt work at all

        Returns:
            str: tradeid list which got moved
        """

        with pymysql.connect(
            host=MYSQL_HOST,
            user=MYSQL_USER,
            password=MYSQL_PASSWORD,
            db=DB_NAME,
            charset="utf8mb4",
        ) as connection:
            table_name = f"{EXCHANGE_TYPE}_{EXECUTION_LEVEL}"
            dead_table_name = f"dead_trade_{EXCHANGE_TYPE}_{EXECUTION_LEVEL}"
            if strat:
                sql_fetch_tradeid = (
                    f"SELECT * FROM {table_name} WHERE STRATEGY LIKE '{strat}' "
                )
                live_trades = pd.read_sql(sql_fetch_tradeid, connection)
                trade_id_list = list(live_trades.TRADEID.astype(str))

            if (trade_id_list is None) or len(trade_id_list) == 0:
                return "no tradeid present in live to move"

            trade_id_list_str = ",".join(trade_id_list)
            cursor = connection.cursor()
            sql_fetch_trades = (
                f"SELECT * FROM {table_name} WHERE TRADEID IN ({trade_id_list_str})"
            )
            live_trades = pd.read_sql(sql_fetch_trades, connection)
            try:
                for row in live_trades.itertuples():
                    sql_replace = f"REPLACE INTO {dead_table_name} VALUES ( {row.TRADEID},'{row.SYMBOL}',{ row.QUANTITY },'{row.STRATEGY}','{row.ENTRY_TIMESTAMP}','{row.SEGMENT}','{row.EXPIRY}','{row.TYPE}', {row.STRIKE}, {row.ENTRY_PRICE},'{ row.ORDER_TYPE }','{row.ENTRY_TIMESTAMP}',{ row.ENTRY_PRICE },'{row.ENTRY_TIMESTAMP_META}','{row.ENTRY_TIMESTAMP_META}','{row.SLAVE_NAME}','{row.EXTENDED_INFO}' );"
                    cursor.execute(sql_replace)
                sql_delete = (
                    f"DELETE FROM {table_name} WHERE TRADEID IN ({trade_id_list_str})"
                )
                cursor.execute(sql_delete)
            except Exception as e:
                cursor.close()
                raise e
            cursor.close()
            connection.commit()
            return trade_id_list_str

    def render(self) -> None:
        moving_option = st.radio("SELECT MOVING CRITERIA :- ", ("TRADEID", "STRATEGY"))
        if moving_option == "TRADEID":
            trade_ids = st.text_input("ENTER TRADE IDS( COMMA SEPERATED ) :- ")
            if st.button("MOVE TRADEID FROM LIVE TO DEAD"):
                tradeid_list = list()
                for tradeid in trade_ids.split(","):
                    tradeid_list.append(tradeid)
                try:
                    tradeid_exits = self.move_live_to_dead(
                        trade_id_list=tradeid_list,
                        strat=None,
                    )
                    logging.info(
                        "Moving process completed for {} : {} by user: {}".format(
                            moving_option, tradeid_exits, self.username
                        )
                    )
                    st.write(
                        "Moving process completed for Trade IDs = {}; Please restart oms for this to take effect".format(
                            tradeid_exits
                        )
                    )
                except Exception as e:
                    logging.info(
                        "Moving process failed for {} : {} by user: {}".format(
                            moving_option, tradeid_list, self.username
                        )
                    )
                    st.write(f"Moving process failed with an Exception: {e}")

        elif moving_option == "STRATEGY":
            if "move_to_dead_strategy_button_clicked" not in st.session_state:
                st.session_state.move_to_dead_strategy_button_clicked = False

            def callback() -> None:
                st.session_state.move_to_dead_strategy_button_clicked = True

            strat = st.text_input("ENTER STRATEGY :- ")
            strat = strat.strip()

            if st.button("MOVE STRATEGY FROM LIVE TO DEAD", on_click=callback) or (
                st.session_state.move_to_dead_strategy_button_clicked == True
            ):
                try:
                    st.session_state.move_to_dead_strategy_button_clicked = False
                    strategy_exits = self.move_live_to_dead(
                        trade_id_list=None, strat=strat
                    )
                    logging.info(
                        "Moving process completed for {} : {} by user: {}".format(
                            moving_option, strategy_exits, self.username
                        )
                    )
                    st.write(
                        "Moving process completed for tradeids = {}, belonging to strategy : {}; Please restart oms for this to take effect".format(
                            strategy_exits, strat
                        )
                    )
                except Exception as e:
                    logging.exception(
                        "Moving process failed with exception: {} for {}: by user: {}".format(
                            repr(e), moving_option, self.username
                        )
                    )
                    st.write(f"Moving process failed with an Exception: {e}")
        else:
            pass
