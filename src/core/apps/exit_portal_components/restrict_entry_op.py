import streamlit as st
from core.apps.exit_portal_components.operations_base import OperationBase
from core.helpers.configstore import EXCHANGE_TYPE
from exchange.helpers.mixins import OmsOpsMixin
import pandas as pd
import balte.balte_config
from time import time
from balte.enums import ExchangeType
import logging
from toti import Toti
from core.helpers.utils import get_live_strats


class RestrictEntryOperationBase(OperationBase, OmsOpsMixin):
    def __init__(self, toti_obj: Toti, username: str) -> None:
        super().__init__()
        self.toti_obj = toti_obj
        self.username = username

    def is_strat_live(self, strat: str) -> bool:
        if strat[:6] == "BaLTE_":
            strat = strat[6:]
        live_strats = get_live_strats()
        return strat in live_strats

    def render(self) -> None:
        select_option = st.radio(
            "SELECT RESTRICTION CRITERIA :- ", ("STRATEGY", "SEGMENT", "SYMBOL")
        )
        if select_option == "STRATEGY":
            strat = st.text_input("ENTER STRATEGY :- ")
            strat = strat.strip()

            # Initialize state
            if "restrict_entry_button_clicked" not in st.session_state:
                st.session_state.restrict_entry_button_clicked = False

            # when outer button was called
            def callback() -> None:
                st.session_state.restrict_entry_button_clicked = True

            if (
                st.button("RESTRICT STRATEGY FROM TAKING ENTRY", on_click=callback)
                or st.session_state.restrict_entry_button_clicked
            ):
                if len(strat) < 6 or strat[:6] != "BaLTE_":
                    st.write(
                        "Are you sure strategy name is correct? As it should start with 'BaLTE_' "
                    )
                    if st.button("YES"):
                        st.session_state.restrict_entry_button_clicked = False
                        self.send_restrict_request(
                            toti_obj=self.toti_obj, criteria=select_option, entity=strat
                        )

                        if not self.is_strat_live(strat):
                            st.write(f"Strategy: {strat} is not live!!")
                            return

                        logging.info(
                            "Restricted Strategy: {} by user: {}".format(
                                strat, self.username
                            )
                        )

                        st.write(f"Restricted Strategy: {strat}")
                else:
                    st.session_state.restrict_entry_button_clicked = False
                    self.send_restrict_request(
                        toti_obj=self.toti_obj, criteria=select_option, entity=strat
                    )

                    if not self.is_strat_live(strat):
                        st.write(f"Strategy: {strat} is not live!!")
                        return

                    logging.info(
                        "Restricted Strategy: {} by user: {}".format(
                            strat, self.username
                        )
                    )

                    st.write(f"Restricted Strategy: {strat}")

        elif select_option == "SEGMENT":
            segment = st.text_input("ENTER SEGMENT :- ")
            segment = segment.strip()

            if st.button("RESTRICT SEGMENT FROM TAKING ENTRY"):
                self.send_restrict_request(
                    toti_obj=self.toti_obj, criteria=select_option, entity=segment
                )

                logging.info(
                    "Restricted Segment: {} by user: {}".format(segment, self.username)
                )

                st.write(f"Restricted Segment: {segment}")
        elif select_option == "SYMBOL":
            symbol = st.text_input("ENTER SYMBOL :- ")
            symbol = (symbol.strip()).upper()

            if st.button("RESTRICT SYMBOL FROM TAKING ENTRY"):
                if symbol not in balte.balte_config.symbol_to_balte_id.keys():
                    raise Exception(f"{symbol} is not a valid symbol!!")

                self.send_restrict_request(
                    toti_obj=self.toti_obj, criteria=select_option, entity=symbol
                )

                logging.info(
                    "Restricted Symbol: {} by user: {}".format(symbol, self.username)
                )

                st.write(f"Restricted Symbol: {symbol}")
        else:
            pass
