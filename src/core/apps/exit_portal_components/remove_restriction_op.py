import streamlit as st
import pandas as pd
from core.apps.exit_portal_components.operations_base import OperationBase
from exchange.helpers.mixins import OmsOpsMixin
from core.helpers.configstore import (
    EXCHANGE_TYPE,
    MINIO_END_POINT,
    MINIO_ACCESS_KEY,
    MINIO_SECRET_KEY,
    MINIO_BUCKET_NAME,
    MINIO_STRATEGY_NOT_TO_TRADE_PATH,
    MINIO_SYMBOL_NOT_TO_TRADE_PATH,
    MINIO_SEGMENT_NOT_TO_TRADE_PATH,
    EXCHANGE_TYPE_MAPPING,
)
from minio import Minio  # type: ignore
import balte.balte_config
from balte.enums import ExchangeType
import logging
from toti import Toti


class RemoveRestrictionOperationBase(OperationBase, OmsOpsMixin):
    def __init__(self, toti_obj: Toti, username: str) -> None:
        super().__init__()
        self.toti_obj = toti_obj
        self.username = username

    def render(self) -> None:
        select_option = st.radio(
            "SELECT REMOVE RESTRICTION CRITERIA :- ",
            ("STRATEGY", "SEGMENT", "SYMBOL"),
        )
        minio_client = Minio(
            MINIO_END_POINT,
            access_key=MINIO_ACCESS_KEY,
            secret_key=MINIO_SECRET_KEY,
            secure=False,
        )
        if select_option == "STRATEGY":
            st.write("STRATEGIES CURRENTLY PRESENT IN RESTRICTION LIST")
            try:
                df = pd.read_csv(  # type: ignore
                    minio_client.get_object(
                        MINIO_BUCKET_NAME,
                        MINIO_STRATEGY_NOT_TO_TRADE_PATH,
                    ),
                    header=None,
                )
                if len(df) == 0:
                    st.write("No strategies present in strategy restriction")
                else:
                    st.write(df)
            except Exception as e:
                logging.exception(
                    "Remove Restriction process failed with exception: {} for {}: by user: {}".format(
                        repr(e), select_option, self.username
                    )
                )
                st.write("No strategies present in strategy restriction")

            strat = st.text_input("ENTER STRATEGY :- ")
            strat = strat.strip()

            # Initialize state
            if "remove_restriction_button_clicked" not in st.session_state:
                st.session_state.remove_restriction_button_clicked = False

            # when outer button was called
            def callback() -> None:
                st.session_state.remove_restriction_button_clicked = True

            if (
                st.button("RE ENTER STRATEGY", on_click=callback)
                or st.session_state.remove_restriction_button_clicked
            ):
                if len(strat) < 6 or strat[:6] != "BaLTE_":
                    st.write(
                        "Are you sure strategy name is correct? As it should start with 'BaLTE_' "
                    )
                    if st.button("YES"):
                        st.session_state.remove_restriction_button_clicked = False
                        self.send_unrestrict_request(
                            toti_obj=self.toti_obj,
                            criteria=select_option,
                            entity=strat,
                        )

                        logging.info(
                            "Removed restriction on Strategy: {} by user: {}".format(
                                strat, self.username
                            )
                        )

                        st.write(f"Removed restriction on Strategy: {strat}")
                else:
                    st.session_state.remove_restriction_button_clicked = False
                    self.send_unrestrict_request(
                        toti_obj=self.toti_obj,
                        criteria=select_option,
                        entity=strat,
                    )

                    logging.info(
                        "Removed restriction on Strategy: {} by user: {}".format(
                            strat, self.username
                        )
                    )

                    st.write(f"Removed restriction on Strategy: {strat}")
        elif select_option == "SEGMENT":
            st.write("SEGMENT CURRENTLY PRESENT IN RESTRICTION LIST")
            try:
                df = pd.read_csv(  # type: ignore
                    minio_client.get_object(
                        MINIO_BUCKET_NAME,
                        MINIO_SEGMENT_NOT_TO_TRADE_PATH,
                    ),
                    header=None,
                )
                if len(df) == 0:
                    st.write("No segment present in segment restriction")
                else:
                    st.write(df)
            except Exception as e:
                logging.exception(
                    "Remove Restriction process failed with exception: {} for {}: by user: {}".format(
                        repr(e), select_option, self.username
                    )
                )
                st.write("No segment present in strategy restriction")

            segment = st.text_input("ENTER SEGMENT :- ")
            segment = segment.strip()

            if st.button("RE ENTER SEGMENT"):
                self.send_unrestrict_request(
                    toti_obj=self.toti_obj,
                    criteria=select_option,
                    entity=segment,
                )
                logging.info(
                    "Removed restriction on Segment: {} by user: {}".format(
                        segment, self.username
                    )
                )

                st.write(f"Removed restriction on Segment: {segment}")
        elif select_option == "SYMBOL":
            st.write("SYMBOLS CURRENTLY PRESENT IN RESTRICTION LIST")
            try:
                df = pd.read_csv(  # type: ignore
                    minio_client.get_object(
                        MINIO_BUCKET_NAME,
                        MINIO_SYMBOL_NOT_TO_TRADE_PATH,
                    ),
                    header=None,
                )
                if len(df) == 0:
                    st.write("No symbols present in symbol restriction")
                else:
                    st.write(df)
            except Exception as e:
                logging.exception(
                    "Remove Restriction process failed with exception: {} for {}: by user: {}".format(
                        repr(e), select_option, self.username
                    )
                )
                st.write("No symbols present in symbol restriction")

            symbol = st.text_input("ENTER SYMBOL :- ")
            symbol = (symbol.strip()).upper()

            if st.button("RE ENTER SYMBOL"):
                if symbol not in balte.balte_config.symbol_to_balte_id.keys():
                    raise Exception(f"{symbol} is not a valid symbol!!")
                self.send_unrestrict_request(
                    toti_obj=self.toti_obj,
                    criteria=select_option,
                    entity=symbol,
                )

                logging.info(
                    "Removed restriction on Symbol: {} by user: {}".format(
                        symbol, self.username
                    )
                )

                st.write(f"Removed restriction on Symbol: {symbol}")
        else:
            pass
