import sys
import datetime
from core.helpers.configstore import WEBHOOK
from core.helpers.alerting import send_message_to_gspace

if __name__ == "__main__":
    timestamp, rest = sys.argv[1].split(maxsplit=1)
    timestamp = f"[{datetime.datetime.fromtimestamp(int(timestamp)/int(1e9)).strftime('%Y-%m-%d %H:%M:%S.%f')}]"
    msg = "\n".join([timestamp, rest])
    send_message_to_gspace(msg=f"<users/all> {msg}", url=WEBHOOK)
