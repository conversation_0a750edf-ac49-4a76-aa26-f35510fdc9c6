from core.runners.runner_base import <PERSON><PERSON><PERSON>, RunnerBaseAsync
import balte.utility_inbuilt
from core.helpers.configstore import EXCHANGE_TYPE


class RunnerBaLTE(RunnerBase):
    """
    Base Class that every Runner using `balte.balte_config` needs to inherit and implement
    """

    def __init__(self) -> None:
        super().__init__()
        balte.utility_inbuilt.balte_initializer(EXCHANGE_TYPE.lower())


class RunnerBaLTEAsync(RunnerBaseAsync):
    """
    Base Class that every Runner using `balte.balte_config` needs to inherit and implement
    """

    def __init__(self) -> None:
        super().__init__()
        balte.utility_inbuilt.balte_initializer(EXCHANGE_TYPE.lower())
