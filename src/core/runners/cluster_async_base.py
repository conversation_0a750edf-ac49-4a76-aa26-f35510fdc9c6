from toti import <PERSON><PERSON>
from balte.engine import AsyncEngineLive
import logging

from core.runners.runner_balte import <PERSON><PERSON><PERSON><PERSON><PERSON>
from typing import Any
from core.helpers.utils import (
    load_strats_pyce,
    filter_strategies,
    get_live_strats,
    import_and_add_strategies,
)
from core.helpers.configstore import (
    EXCHANGE_TYPE,
    EXECUTION_LEVEL,
    MINIO_ACCESS_KEY,
    MINIO_SECRET_KEY,
    MINIO_BUCKET_NAME,
    MINIO_END_POINT,
    KAFKA_HOST,
    KAFKA_PORT,
    OMS_HOST,
    OMS_PORT,
)
from typing import List


class ClusterAsyncRunnerBase(RunnerBaLTE):
    def __init__(self, clusters_to_be_delayed: List[str]) -> None:
        super().__init__()
        Toti.set_production_mode(f"{OMS_HOST}:{OMS_PORT}")
        self.clusters_to_be_delayed = clusters_to_be_delayed

    def add_strats(self) -> None:
        to_be_live = get_live_strats()
        to_be_live = [i for i in to_be_live if "cluster" == i[:7]]
        # IMPORTANT: add order of clusters for sync flag
        slave_clusters = [
            i for i in to_be_live if (i not in self.clusters_to_be_delayed)
        ]
        master_clusters = [i for i in self.clusters_to_be_delayed if i in to_be_live]
        to_be_live = slave_clusters + master_clusters
        load_strats_pyce(to_be_live)
        to_be_live = filter_strategies(to_be_live, filter_async=True)
        import_and_add_strategies(self.obj, to_be_live, preserve_order=True)

    def initialize(self) -> None:
        # Disable Logging From Supporting Modules  IPKernelApp schedule
        logger = logging.getLogger("schedule")
        logger.propagate = False
        logger = logging.getLogger("IPKernelApp")
        logger.propagate = False

        # balte.balte_config.BACKUP_LOCATION = "/home/<USER>/LIVE_TRADING/pickles/"
        self.obj = AsyncEngineLive(
            log_filename=f"/opt/balte_live/log/CLUSTER/{EXCHANGE_TYPE}_{EXECUTION_LEVEL}_Live_async.log",
            log_level="INFO",
            log_filemode="a",
            engine_tag=f"{EXCHANGE_TYPE}_{EXECUTION_LEVEL}_CLUSTER_ASYNC",
        )
        self.obj.set_kafka_message_processor(
            kafka_ip=f"{KAFKA_HOST}:{KAFKA_PORT}",
            kafka_group_id=f"{EXECUTION_LEVEL}_{EXCHANGE_TYPE}_async_engine_consumer",
            slave_engines=[f"{EXCHANGE_TYPE}_{EXECUTION_LEVEL}_SLAVE"],
            engine_name=f"{EXCHANGE_TYPE}_{EXECUTION_LEVEL}_CLUSTER_ASYNC",
            kafka_topics_consume=[f"{EXECUTION_LEVEL}_{EXCHANGE_TYPE}_async_msgs_oms"],
            kafka_topic_alert=f"balte_{EXECUTION_LEVEL}_engines_{EXCHANGE_TYPE}",
        )
        self.obj.add_minio_path_for_pickles(
            MINIO_END_POINT=MINIO_END_POINT,
            MINIO_ACCESS_KEY=MINIO_ACCESS_KEY,
            MINIO_SECRET_KEY=MINIO_SECRET_KEY,
            MINIO_BUCKET_NAME=MINIO_BUCKET_NAME,
        )

        self.add_strats()

    def run(self, *args: Any, **kwargs: Any) -> None:
        self.initialize()

        ## Use this when there is an issue with running clusters asynchronously. This will run clusters sequentially in the specified order.
        ## ENSURE the `to_be_live` order is correct - slave clusters should come first in the list.

        # self.obj.set_flag_to_run_clusters_in_sync()
        # self.obj.enable_periodic_master_trade_update(master_clusters)

        self.obj.run_trading(mode=EXECUTION_LEVEL.upper())
