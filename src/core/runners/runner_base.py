from abc import ABC, abstractmethod


class RunnerBase(ABC):
    """
    Base Class that every Runner needs to inherit and implement
    """

    def __init__(self) -> None:
        super().__init__()

    @abstractmethod
    def run(self, *args, **kwargs) -> None:  # type: ignore
        """
        Every Runner needs to implement this function which should execute logic needed to at runtime
        """
        pass


class RunnerBaseAsync(ABC):
    """
    Base Class that every Runner needs to inherit and implement
    """

    def __init__(self) -> None:
        super().__init__()

    @abstractmethod
    async def run(self, *args, **kwargs) -> None:  # type: ignore
        """
        Every Runner needs to implement this function which should execute logic needed to at runtime
        """
        pass
