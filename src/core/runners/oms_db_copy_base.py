import pymysql

from core.helpers.configstore import (
    EXCHANGE_TYPE,
    EXECUTION_LEVEL,
    MYSQL_HOST,
    MYSQL_USER,
    MYSQL_PASSWORD,
    DB_NAME,
)
from typing import Any
from core.runners.runner_base import RunnerBase


class OMSDBCopyRunnerBase(RunnerBase):
    def __init__(self) -> None:
        super().__init__()

    def run(self, *args: Any, **kwargs: Any) -> None:
        """Append intraday table to the total table.
        Also empty the intraday table for the next day.

        Raises:
            ValueError: if appending intraday table fails
            ValueError: if emptying intraday table fails
        """
        connection = pymysql.connect(
            host=MYSQL_HOST,
            user=MYSQL_USER,
            password=MYSQL_PASSWORD,
            db=DB_NAME,
            charset="utf8mb4",
        )
        with connection.cursor() as cursor:
            try:
                query = "INSERT INTO {} SELECT * FROM {}".format(
                    f"dead_trade_{EXCHANGE_TYPE}_{EXECUTION_LEVEL}_total",
                    f"dead_trade_{EXCHANGE_TYPE}_{EXECUTION_LEVEL}",
                )
                cursor.execute(query)
            except Exception as e:
                raise ValueError(f"Appending intraday table failed with error {e}")
            try:
                query = "DELETE FROM {}".format(
                    f"dead_trade_{EXCHANGE_TYPE}_{EXECUTION_LEVEL}"
                )
                cursor.execute(query)
            except Exception as e:
                raise ValueError(f"Clearing intraday table failed with {e}")
        connection.commit()
        connection.close()
