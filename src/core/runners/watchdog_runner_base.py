from core.helpers.configstore import (
    EXCHANGE_TYPE,
    EXECUTION_LEVEL,
    MINIO_ACCESS_KEY,
    MINIO_SECRET_KEY,
    MINIO_BUCKET_NAME,
    MINIO_END_POINT,
    KAFKA_HOST,
    KAFKA_PORT,
    OMS_HOST,
    OMS_PORT,
)
from toti import Toti
from balte.engine import EngineLive
import balte
from typing import Any

from exchange.helpers.watchdog import watchdog

# Disable Logging From Supporting Modules
import logging

from core.runners.runner_balte import RunnerBaLTE


class WatchdogRunnerBase(RunnerBaLTE):
    def __init__(self) -> None:
        super().__init__()
        # MOUNT X drive to run this strat # Directly reads data from datasnap file
        # KRX market timings in KST
        balte.balte_config.MARKET_CALL_INITIALIZE_HOUR = 8
        balte.balte_config.MARKET_CALL_INITIALIZE_MINUTE = 30

        balte.balte_config.MARKET_CALL_AFTER_TRADING_HOUR = 15
        balte.balte_config.MARKET_CALL_AFTER_TRADING_MINUTE = 45
        # Adding Kafka IP to make sure all kafka related msgs move to respective kafka, instead of default nse
        balte.balte_config.KAFKA_IP = f"{KAFKA_HOST}:{KAFKA_PORT}"
        Toti.set_production_mode(f"{OMS_HOST}:{OMS_PORT}")

    def run(self, *args: Any, **kwargs: Any) -> None:
        logger = logging.getLogger("schedule")
        logger.propagate = False
        logger = logging.getLogger("IPKernelApp")
        logger.propagate = False

        obj = EngineLive(
            log_filename=f"/opt/balte_live/log/WATCHDOG/{EXCHANGE_TYPE}_{EXECUTION_LEVEL}_Live.log",
            log_level="INFO",
            log_filemode="a",
            engine_tag=f"{EXCHANGE_TYPE}_{EXECUTION_LEVEL}_WATCHDOG",
        )
        obj.add_minio_path_for_pickles(
            MINIO_END_POINT=MINIO_END_POINT,
            MINIO_ACCESS_KEY=MINIO_ACCESS_KEY,
            MINIO_SECRET_KEY=MINIO_SECRET_KEY,
            MINIO_BUCKET_NAME=MINIO_BUCKET_NAME,
        )

        pickle_lst = []
        class_list = []
        if obj.check_if_pickle_exists("watchdog") is not None:
            pickle_lst.append("watchdog")
        else:
            class_list.append(watchdog)

        obj.add_strategy_or_pickle(
            pickle_list=pickle_lst, class_name_list=class_list, load_last_timestamp=True
        )

        obj.enable_kafka_logging(
            f"{KAFKA_HOST}:{KAFKA_PORT}",
            f"balte_{EXECUTION_LEVEL}_engines_{EXCHANGE_TYPE}",
            f"WATCHDOG_{EXCHANGE_TYPE}_{EXECUTION_LEVEL}",
        )

        obj.run_trading()
