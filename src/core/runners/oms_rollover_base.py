from toti.rpc import oms_pb2
from toti.rpc import oms_pb2_grpc
import grpc
from typing import Any, Dict, List

from core.helpers.configstore import (
    OMS_HOST,
    OMS_PORT,
)

from core.runners.runner_base import RunnerBase


class OMSRolloverRunnerBase(RunnerBase):
    def __init__(self) -> None:
        super().__init__()

    def process_cluster_info(
        self,
        curr_rollover_info: List[Dict[str, str]],
    ) -> Dict[str, Dict[str, Any]]:
        """
        Convert curr_rollover_info to a dict of dict where each value
        represents next entry and rollover info
        """
        rollover_info = {}
        for item in curr_rollover_info:
            if "name" not in item:
                continue
            try:
                temp_dict = {}
                temp_dict["entry_next"] = int(item["entry_next"])
                temp_dict["rollover"] = item["rollover"].lower() in ["true"]
            except Exception as e:
                print(
                    f"received the following exception {repr(e)} when processing {item['name']}"
                )
                continue
            rollover_info[item["name"]] = temp_dict
        return rollover_info

    def send_rpc_for_rollover(self, items_to_rollover_list: List[str]) -> None:
        """
        1- Accept a list of strategies
        2- Create grpc client for OMS test and prod server
        3- Apply rollover for the given list of strategies
        """
        prod_oms_stub = oms_pb2_grpc.OrderManagementServiceStub(
            grpc.insecure_channel(f"{OMS_HOST}:{OMS_PORT}")
        )

        try:
            for strat in items_to_rollover_list:
                msg = prod_oms_stub.apply_rollover(
                    oms_pb2.ApplyRolloverRequest(strategy=strat)
                )
                if msg.message != "Successful":
                    raise Exception(f"error in processing rollover for strat {strat}")
        except Exception as e:
            print("issue in prod oms apply rollover function")
            print(f"issue in strat {strat}")
            raise e

    def run(self, *args: Any, **kwargs: Any) -> None:
        # TO BE IMPLEMENTED LATER
        pass
