from core.runners.runner_balte import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from aiokafka import AIOKafkaConsumer  # type: ignore
import datetime
from typing import Dict, Callable, Coroutine, Any

from core.helpers.configstore import (
    EXCHANGE_TYPE,
    EXECUTION_LEVEL,
    KAFKA_HOST,
    KAFKA_PORT,
    WEBHOOK,
)
from core.helpers.alerting import send_message_to_gspace
import asyncio
import balte.balte_config


class RunnerLiveBotBase(RunnerBaLTEAsync):
    """
    Base Class that every Runner using `balte.balte_config` needs to inherit and implement
    """

    def __init__(self) -> None:
        super().__init__()
        self.consumer_tasks: Dict[
            AIOKafkaConsumer, Callable[[Any], Coroutine[Any, Any, Any]]
        ] = {}

    async def add_consumer_and_task(self) -> None:
        balte_consumer = AIOKafkaConsumer(
            f"balte_{EXECUTION_LEVEL}_engines_{EXCHANGE_TYPE}",
            bootstrap_servers=f"{KAFKA_HOST}:{KAFKA_PORT}",
            auto_offset_reset="earliest",
            consumer_timeout_ms=360000,
            loop=asyncio.get_event_loop(),
        )
        oms_consumer = AIOKafkaConsumer(
            f"balte_oms_{EXECUTION_LEVEL}_logs_{EXCHANGE_TYPE}",
            bootstrap_servers=f"{KAFKA_HOST}:{KAFKA_PORT}",
            auto_offset_reset="earliest",
            consumer_timeout_ms=360000,
            loop=asyncio.get_event_loop(),
        )
        self.consumer_tasks[balte_consumer] = RunnerLiveBotBase.process_balte_messages
        self.consumer_tasks[oms_consumer] = RunnerLiveBotBase.process_oms_logs

    @staticmethod
    async def process_balte_messages(balte_consumer: AIOKafkaConsumer) -> None:
        async for msg in balte_consumer:
            await asyncio.sleep(0.0000001)  # Release event loop
            current_time = datetime.datetime.now()
            msg = msg.value.decode("utf-8")

            time_str = f"{balte.balte_config.MARKET_CALL_INITIALIZE_HOUR:02}:{balte.balte_config.MARKET_CALL_INITIALIZE_MINUTE:02}"
            # assert msg == "", f"{msg} {current_time}  {time_str}"
            if (
                msg[-6:-1] == time_str
                and f"INFO {EXCHANGE_TYPE}_{EXECUTION_LEVEL}_SLAVEModule:EngineLive Called RunFullLive END"
                in msg
            ):
                msg_date = datetime.datetime.strptime(
                    msg[1:20], "%Y-%m-%d %H:%M:%S"
                )  ## msg timestamp for logs will always be IST(local time), hence no special handling for timezone is required
                if (current_time - msg_date).total_seconds() > 300:
                    continue
                send_message_to_gspace(
                    msg=f"{EXCHANGE_TYPE}_{EXECUTION_LEVEL}_SLAVE Module initialize ran successfully",
                    url=WEBHOOK,
                )

            if len(msg.split(" ")) > 2 and (msg.split(" ")[2] in ["ERROR", "CRITICAL"]):
                msg_date = datetime.datetime.strptime(
                    msg[1:20], "%Y-%m-%d %H:%M:%S"
                )  ## msg timestamp for logs will always be IST(local time), hence no special handling for timezone is required
                if (current_time - msg_date).total_seconds() > 300:
                    continue
                send_message_to_gspace(msg=msg, url=WEBHOOK)

            ## note: RejectedExitTradeFromOMS are already considered in above case since they are already logged as "ERROR"
            ## so, considering RejectedEntryTradeFromOMS case here, since they are as of now logged as INFO
            # assert msg == "", f'{msg} , {len(msg.split(" "))}'
            if len(msg.split(" ")) > 5 and (
                msg.split(" ")[4] == "RejectedEntryTradeFromOMS"
            ):
                msg_date = datetime.datetime.strptime(
                    msg[1:20], "%Y-%m-%d %H:%M:%S"
                )  ## msg timestamp for logs will always be IST(local time), hence no special handling for timezone is required
                if (current_time - msg_date).total_seconds() > 300:
                    continue
                send_message_to_gspace(msg=msg, url=WEBHOOK)

    @staticmethod
    async def process_oms_logs(oms_consumer: AIOKafkaConsumer) -> None:
        async for msg in oms_consumer:
            await asyncio.sleep(0.0000001)  # Release event loop
            current_time = datetime.datetime.now()
            msg = msg.value.decode("utf-8")

            if len(msg.split(" ")) > 3 and (msg.split(" ")[2] in ["ERROR", "CRITICAL"]):
                msg_date = datetime.datetime.strptime(
                    msg[1:20], "%Y-%m-%d %H:%M:%S"
                )  ## msg timestamp for logs will always be IST(local time), hence no special handling for timezone is required
                if (current_time - msg_date).total_seconds() > 300:
                    continue
                send_message_to_gspace(msg=msg, url=WEBHOOK)

    async def run(self) -> None:
        await self.add_consumer_and_task()
        tasks = []
        for consumer, task in self.consumer_tasks.items():
            await consumer.start()
            tasks.append(task(consumer))

        await asyncio.gather(*tasks)
