import toti.toti_config
from toti import Toti

import balte
from balte.engine import EngineLive
import balte.balte_config
import balte.utility_inbuilt
from typing import List, Any
from analytics_api import AnalyticsAPI

# Disable Logging From Supporting Modules
import logging
from core.helpers.configstore import (
    EXCHANGE_TYPE,
    EXECUTION_LEVEL,
    SEGMENT,
    MINIO_ACCESS_KEY,
    MINIO_SECRET_KEY,
    MINIO_BUCKET_NAME,
    MINIO_END_POINT,
    KAFKA_HOST,
    KAFKA_PORT,
    OMS_HOST,
    OMS_PORT,
)
from core.helpers.utils import (
    load_strats_pyce,
    get_live_strats,
    import_and_add_strategies,
)
from core.runners.runner_balte import RunnerBaLTE


class SlaveRunnerBase(RunnerBaLTE):
    def __init__(self) -> None:
        super().__init__()

        toti.toti_config.DATABASE_INFO["live_table"] = (
            f"{EXCHANGE_TYPE}_{EXECUTION_LEVEL}"
        )
        toti.toti_config.DATABASE_INFO["dead_table"] = (
            f"{EXCHANGE_TYPE}_dead_trade_{EXECUTION_LEVEL}"
        )
        Toti.set_production_mode(f"{OMS_HOST}:{OMS_PORT}")
        # Adding Kafka IP to make sure all kafka related msgs move to respective kafka, instead of default nse
        balte.balte_config.KAFKA_IP = f"{KAFKA_HOST}:{KAFKA_PORT}"

    def add_strats(self) -> None:
        to_be_live = get_live_strats()
        to_be_live = [strat for strat in to_be_live if "cluster" not in strat]
        load_strats_pyce(to_be_live)
        import_and_add_strategies(self.obj, to_be_live)

    def initialize(self) -> None:
        logger = logging.getLogger("schedule")
        logger.propagate = False
        logger = logging.getLogger("IPKernelApp")
        logger.propagate = False

        # # LOAD STRATEGIES AND PREP ENGINE
        self.obj = EngineLive(
            log_filename=f"/opt/balte_live/log/SLAVE/{EXCHANGE_TYPE}_{EXECUTION_LEVEL}_Live.log",
            log_level="INFO",
            log_filemode="a",
            engine_tag=f"{EXCHANGE_TYPE}_{EXECUTION_LEVEL}_SLAVE",
        )

        self.obj.add_minio_path_for_pickles(
            MINIO_END_POINT=MINIO_END_POINT,
            MINIO_ACCESS_KEY=MINIO_ACCESS_KEY,
            MINIO_SECRET_KEY=MINIO_SECRET_KEY,
            MINIO_BUCKET_NAME=MINIO_BUCKET_NAME,
        )

        # by doing this, even logging.exception will come to kafka
        self.obj.enable_kafka_logging(
            f"{KAFKA_HOST}:{KAFKA_PORT}",
            f"balte_{EXECUTION_LEVEL}_engines_{EXCHANGE_TYPE}",
            f"{EXCHANGE_TYPE}_{EXECUTION_LEVEL}_SLAVE",
        )

        self.add_strats()

    def run(self, *args: Any, **kwargs: Any) -> None:
        self.initialize()

        self.obj.run_trading()
