from core.helpers.configstore import (
    EXCHANGE_TYPE,
    EXECUTION_LEVEL,
    SEGMENT,
    MINIO_ACCESS_KEY,
    MINIO_SECRET_KEY,
    MINIO_BUCKET_NAME,
    MINIO_END_POINT,
    KAFKA_HOST,
    KAFKA_PORT,
    OMS_HOST,
    OMS_PORT,
)
from core.helpers.utils import (
    load_strats_pyce,
    filter_strategies,
    get_live_strats,
    import_and_add_strategies,
)

import pandas as pd
from kafka import KafkaConsumer  # type: ignore
import time
from typing import List, Dict, Set, Any
from analytics_api import AnalyticsAPI

# Disable Logging From Supporting Modules
import logging

import toti.toti_config
from toti import Toti

import balte
from balte.engine import EngineLive
import balte.balte_config
import balte.utility_inbuilt
from core.helpers.utils import get_local_timestamp
from core.runners.runner_balte import RunnerBaLTE


class ClusterRunnerBase(RunnerBaLTE):
    def __init__(self) -> None:
        super().__init__()
        self.cluster_exit_time: str = "15:35"
        toti.toti_config.DATABASE_INFO["live_table"] = (
            f"{EXCHANGE_TYPE}_{EXECUTION_LEVEL}"
        )
        toti.toti_config.DATABASE_INFO["dead_table"] = (
            f"{EXCHANGE_TYPE}_dead_trade_{EXECUTION_LEVEL}"
        )
        Toti.set_production_mode(f"{OMS_HOST}:{OMS_PORT}")
        # Adding Kafka IP to make sure all kafka related msgs move to respective kafka, instead of default nse
        balte.balte_config.KAFKA_IP = f"{KAFKA_HOST}:{KAFKA_PORT}"

    def update_slaves(self) -> None:
        analytics_api_obj = AnalyticsAPI(mode=EXECUTION_LEVEL)
        for i in range(len(self.obj.strategy)):
            name = self.obj.strategy[i]._name
            print("Updating Slave list for:", name)
            try:
                slaves = analytics_api_obj.get_slaves(name)
                mastertrade_index = 0
                for v in range(len(self.obj.strategy[i].universe_meta)):
                    if self.obj.strategy[i].universe_meta[v].name == "mastertrade":
                        mastertrade_index = v
                        break
                self.obj.strategy[i].universe_meta[mastertrade_index].slaves = slaves
            except Exception as e:
                print(f"Failed to update Slave list for {name} with error {repr(e)}")

    def add_strats(self) -> None:
        to_be_live = get_live_strats()
        to_be_live = [i for i in to_be_live if "cluster" == i[:7]]
        load_strats_pyce(to_be_live)
        to_be_live = filter_strategies(to_be_live, filter_async=False)
        import_and_add_strategies(self.obj, to_be_live)

    def initialize(self) -> None:
        logger = logging.getLogger("schedule")
        logger.propagate = False
        logger = logging.getLogger("IPKernelApp")
        logger.propagate = False

        # LOAD STRATEGIES AND PREP ENGINE
        self.obj = EngineLive(
            log_filename=f"/opt/balte_live/log/CLUSTER/{EXCHANGE_TYPE}_{EXECUTION_LEVEL}_Live.log",
            log_level="INFO",
            log_filemode="a",
            engine_tag=f"{EXCHANGE_TYPE}_{EXECUTION_LEVEL}_CLUSTER",
        )

        self.obj.add_minio_path_for_pickles(
            MINIO_END_POINT=MINIO_END_POINT,
            MINIO_ACCESS_KEY=MINIO_ACCESS_KEY,
            MINIO_SECRET_KEY=MINIO_SECRET_KEY,
            MINIO_BUCKET_NAME=MINIO_BUCKET_NAME,
        )

        # by doing this, even logging.exception will come to kafka
        self.obj.enable_kafka_logging(
            f"{KAFKA_HOST}:{KAFKA_PORT}",
            f"balte_{EXECUTION_LEVEL}_engines_{EXCHANGE_TYPE}",
            f"{EXCHANGE_TYPE}_{EXECUTION_LEVEL}_CLUSTER",
        )

        self.add_strats()

        # # SCHEDULE JOBS
        # ### KAFKA TRIGGER
        logger = logging.getLogger("kafka")
        logger.propagate = False
        self.consumer = KafkaConsumer(
            f"balte_{EXECUTION_LEVEL}_engines_{EXCHANGE_TYPE}",
            bootstrap_servers=f"{KAFKA_HOST}:{KAFKA_PORT}",
            auto_offset_reset="smallest",
        )

    def run(self, *args: Any, **kwargs: Any) -> None:
        self.initialize()

        slave_timestamp: Dict[str, Set[pd.Timestamp]] = {
            f"{EXCHANGE_TYPE}_{EXECUTION_LEVEL}_SLAVE": set()
        }
        counter = {}
        date = get_local_timestamp().strftime("%Y-%m-%d")
        first_bar_flag = True
        for msg in self.consumer:
            msg = msg.value.decode("utf-8")
            if ("RunFullLive END" in msg) and (date in msg.split("RunFullLive END")[1]):
                slave = msg[: msg.index("Module:")].split()[-1]
                time_str = msg[-6:-1]
                print(time_str)
                timestamp = pd.Timestamp(msg[-31:-12]).replace(
                    hour=int(time_str.split(":")[0]), minute=int(time_str.split(":")[1])
                )

                if slave not in slave_timestamp:
                    continue

                print("DETECTED :", timestamp, time_str, slave)

                # Update Per slave timestamp
                if timestamp not in slave_timestamp[slave]:
                    slave_timestamp[slave].add(timestamp)

                    if timestamp not in counter:
                        counter[timestamp] = 1
                    else:
                        counter[timestamp] += 1

                    if counter[timestamp] == len(slave_timestamp):
                        print(
                            "Calling for {} with string {}".format(timestamp, time_str)
                        )
                        if first_bar_flag:
                            # this is done for initialize usually.
                            self.update_slaves()
                            first_bar_flag = False
                        self.obj.run_full_live(time_str)
                        if time_str == self.cluster_exit_time:
                            time.sleep(5)
                            print("EXITING SCRIPT")
                            break
                else:
                    print(
                        "CRITICAL: SLAVE {} called again for {}".format(
                            slave, timestamp
                        )
                    )
