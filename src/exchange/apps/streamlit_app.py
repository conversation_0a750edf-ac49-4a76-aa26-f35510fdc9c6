from core.apps.streamlit_app_base import StreamlitAppRunnerBase
import streamlit as st
from typing import List
from exchange.apps.exit_portal_components.exit_portal import ExitPortal


class StreamlitAppRunner(StreamlitAppRunnerBase):
    pass


if __name__ == "__main__":
    if "exit_portal" not in st.session_state:
        st.session_state.exit_portal = ExitPortal()
    app = StreamlitAppRunner(
        pages=[
            st.Page(st.session_state.exit_portal.run, title="Exit Portal"),
        ]
    )
    app.run()
