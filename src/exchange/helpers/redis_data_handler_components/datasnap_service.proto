syntax = "proto3";

package datasnapservice;

service DataSnap {
  rpc get_data (DataSnapRequest) returns (RepeatedArrayReply) {}
  rpc get_latest_second (TimeStampRequest) returns (TimestampResponse) {}
}

message DataSnapRequest {
  string segment = 1;
  string timestamp = 2;
  string encoding = 3;
}

message TimeStampRequest {
  string segment = 1;
}

message RepeatedArrayReply {
  repeated string message = 1 ;
}

message TimestampResponse {
  string timestamp = 1;
}
