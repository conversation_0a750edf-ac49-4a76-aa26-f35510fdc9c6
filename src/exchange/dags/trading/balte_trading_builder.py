from core.dags.trading.balte_trading_builder_base import (
    BalteTradingDagBuilderBase,
)
from core.helpers.configstore import EXCHANGE_TYPE, EXECUTION_LEVEL
from airflow.models import DAG


class BalteTradingDagBuilder(BalteTradingDagBuilderBase):
    """
    Each exchange can override this file to add exchange specific behaviour
    to each dag
    """

    pass


balte_trading_dag_builder = BalteTradingDagBuilder(
    dag_id=f"BaLTE_Trading_Infra_{EXCHANGE_TYPE}_{EXECUTION_LEVEL}",
    schedule_interval="30 08 * * 1-5",  # 08:30 AM
)
dag = balte_trading_dag_builder.build_dag()
