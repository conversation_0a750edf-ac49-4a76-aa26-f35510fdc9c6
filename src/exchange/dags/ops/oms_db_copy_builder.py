from core.dags.ops.oms_ops_builder_base import (
    OMSDBCopyDagBuilderBase,
)
from airflow.models import DAG


class OMSDBCopyDagBuilder(OMSDBCopyDagBuilderBase):
    """
    Each exchange can override this file to add exchange specific behaviour
    to each dag
    """

    pass


oms_db_copy_dag_builder = OMSDBCopyDagBuilder(
    dag_id="OMS_DB_COPY", schedule_interval="57 23 * * 1-5"
)
dag = oms_db_copy_dag_builder.build_dag()
