from core.dags.ops.dead_strat_exit_builder_base import (
    DeadStratExitDagBuilderBase,
)
from airflow.models import DAG
import pandas as pd
from core.helpers.utils import get_local_date
import balte.balte_config


class DeadStratExitDagBuilder(DeadStratExitDagBuilderBase):
    """
    Each exchange can override this file to add exchange specific behaviour
    to each dag
    """

    def decide_exit_timestamp(self) -> pd.Timestamp:
        """
        this function is used to determine when to send exit for slave level trades.
        """
        local_date = get_local_date()
        if local_date in balte.balte_config.ALL_DATES:
            return local_date.replace(hour=9, minute=5)
        raise Exception("today is not in ALL_DATES")

    def decide_exit_timestamp_meta(self) -> pd.Timestamp:
        """
        this function is used to determine what to keep exit_timestamp in db for corresponding exit.
        """
        local_date = get_local_date()
        if local_date in balte.balte_config.ALL_DATES:
            return local_date.replace(hour=9, minute=5)
        raise Exception("today is not in ALL_DATES")


dead_strat_exit_dag_builder = DeadStratExitDagBuilder(
    dag_id="removed_strat_exit", schedule_interval="45 8 * * 1-5"
)
dag = dead_strat_exit_dag_builder.build_dag()
