from core.dags.dag_builder_base import Dag<PERSON>uilderBase

from airflow.models import DAG
from airflow.operators.bash import BashOperator
from core.helpers.configstore import CONDA_ENV_NAME, EXCHANGE_TYPE, EXECUTION_LEVEL
from airflow.operators.email import EmailOperator
from airflow.utils.trigger_rule import TriggerRule


def build_email_task(dag: DAG) -> EmailOperator:
    return EmailOperator(
        task_id="email_task",
        to=["<EMAIL>", "Balte_alerts.kivicapital.in"],
        html_content=""" <h3>Redis data handler stopped </h3> """,
        subject=f"Problem in Redis data handler of {EXCHANGE_TYPE}_{EXECUTION_LEVEL}",
        trigger_rule=TriggerRule.ONE_FAILED,
        dag=dag,
    )


class RedisDataHandlerDagBuilder(DagBuilderBase):
    def __init__(self, dag_id: str, schedule_interval: str) -> None:
        super().__init__(
            dag_id=dag_id,
            schedule_interval=schedule_interval,
        )

    def build_dag(self) -> DAG:
        dag = DAG(
            dag_id=self.dag_id,
            schedule_interval=self.schedule_interval,
            default_args=self.dag_default_args,
            catchup=self.dag_catchup,
        )
        BashOperator(
            task_id="REDIS_DATA_HANDLER",
            bash_command=f"/opt/conda/envs/{CONDA_ENV_NAME}/bin/python /app/src/exchange/runners/redis_data_handler.py",
            dag=dag,
        ) >> build_email_task(dag)
        return dag


redis_data_handler_builder = RedisDataHandlerDagBuilder(
    dag_id="REDIS_DATA_HANDLER", schedule_interval="00 08 * * 1-5"
)
dag = redis_data_handler_builder.build_dag()
