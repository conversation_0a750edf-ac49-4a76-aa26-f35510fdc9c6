from core.dags.ops.oms_ops_builder_base import (
    OMSLatencyCheckDagBuilderBase,
)
from airflow.models import DAG


class OMSLatencyCheckDagBuilder(OMSLatencyCheckDagBuilderBase):
    """
    Each exchange can override this file to add exchange specific behaviour
    to each dag
    """

    pass


oms_latency_check_dag_builder = OMSLatencyCheckDagBuilder(
    dag_id="OMS_LATENCY_CHECK", schedule_interval="57 23 * * 1-5"
)
dag = oms_latency_check_dag_builder.build_dag()
