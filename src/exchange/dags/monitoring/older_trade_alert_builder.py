from core.dags.monitoring.older_trade_alert_builder_base import (
    OlderTradeAlertDagBuilderBase,
)
from core.helpers.configstore import EXCHANGE_TYPE, EXECUTION_LEVEL
from airflow.models import DAG


class OlderTradeAlertDagBuilder(OlderTradeAlertDagBuilderBase):
    """
    Each exchange can override this file to add exchange specific behaviour
    to each dag
    """

    pass


older_trade_alert_dag_builder = OlderTradeAlertDagBuilder(
    dag_id=f"older_trade_alert_{EXCHANGE_TYPE}_{EXECUTION_LEVEL}",
    schedule_interval="57 23 * * 1-5",
)
dag = older_trade_alert_dag_builder.build_dag()
