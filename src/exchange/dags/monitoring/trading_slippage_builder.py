from core.dags.monitoring.trading_slippage_builder_base import (
    TradingSlippageDagBuilderBase,
)
from airflow.models import DAG
from core.helpers.configstore import (
    EXCHANGE_TYPE,
    EXECUTION_LEVEL,
)


class TradingSlippageDagBuilder(TradingSlippageDagBuilderBase):
    """
    Each exchange can override this file to add exchange specific behaviour
    to each dag
    """

    pass


trading_slippage_dag_builder = TradingSlippageDagBuilder(
    dag_id=f"Slippage_{EXCHANGE_TYPE}_{EXECUTION_LEVEL}",
    schedule_interval="55 08 * * 1-5",
)
dag = trading_slippage_dag_builder.build_dag()
