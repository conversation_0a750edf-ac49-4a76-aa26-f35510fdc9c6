from core.runners.one_second_exit_application_base import (
    OMSOneSecondExitApplicationRunnerBase,
)


class OMSOneSecondExitApplicationRunner(OMSOneSecondExitApplicationRunnerBase):
    """
    Each exchange can override this file to add exchange specific behaviour
    to each runner
    """

    pass


if __name__ == "__main__":
    oms_one_second_exit_application_runner = OMSOneSecondExitApplicationRunner()
    oms_one_second_exit_application_runner.run()
