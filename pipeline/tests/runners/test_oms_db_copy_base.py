import pytest
from test_data.datastore import SAMPLE_DEAD_TRADES
from core.helpers.configstore import (
    EXCHANGE_TYPE,
    EXECUTION_LEVEL,
)
import pandas as pd

TABLE_SETUP_PARAM = {
    "name": [
        f"dead_trade_{EXCHANGE_TYPE}_{EXECUTION_LEVEL}",
        f"dead_trade_{EXCHANGE_TYPE}_{EXECUTION_LEVEL}_total",
    ],
    "value": [pd.DataFrame(SAMPLE_DEAD_TRADES), pd.DataFrame()],
}


@pytest.mark.parametrize("table_setup", [TABLE_SETUP_PARAM], indirect=True)
def test_run(oms_db_copy_runner, table_setup):
    oms_db_copy_runner.run()
    assert len(oms_db_copy_runner.get_all_dead_trades()) == 0
    assert (
        len(
            oms_db_copy_runner.filter_trades(
                query="SELECT * FROM dead_trade_nse_test_total",
            )
        )
        == 2
    )
