import pytest
from unittest.mock import MagicMock, AsyncMock
import pandas as pd
from test_data.datastore import DEAD_TRADES_TO_INSERT, LIVE_TRADES_TO_INSERT


@pytest.mark.asyncio
async def test_consume_kafka_messages_and_update_positions(
    custom_one_second_exit_application_runner,
):
    await custom_one_second_exit_application_runner.consume_kafka_messages_and_update_positions()
    custom_one_second_exit_application_runner.kafka_trade_processor.read_from_kafka_and_process.assert_awaited_once()


@pytest.mark.asyncio
async def test_market_termination_checker(
    custom_one_second_exit_application_runner, mocker
):
    mocker.patch(
        "core.runners.one_second_exit_application_base.get_local_timestamp",
        side_effect=[
            pd.Timestamp(2025, 3, 27, 15, 29, 0),
            pd.Timestamp(2025, 3, 27, 15, 29, 0),
        ],
    )
    mock_sleep = mocker.patch("asyncio.sleep", new_callable=AsyncMock)
    await custom_one_second_exit_application_runner.market_termination_checker()
    mock_sleep.assert_called_once_with(60)
    assert custom_one_second_exit_application_runner.is_market_closed.done()
    assert custom_one_second_exit_application_runner.kafka_trade_processor.is_market_closed.done()


@pytest.mark.parametrize(
    "table_setup",
    [
        {
            "name": ["dead_trade_nse_test", "nse_test"],
            "value": [
                pd.DataFrame(DEAD_TRADES_TO_INSERT),
                pd.DataFrame(LIVE_TRADES_TO_INSERT),
            ],
        }
    ],
    indirect=True,
)
@pytest.mark.asyncio
async def test_fetch_data_and_check_exit_conditions(
    custom_one_second_exit_application_runner, table_setup, mocker
):
    custom_one_second_exit_application_runner.position_manager.initialize_position_manager_from_database()
    mock_is_market_closed = MagicMock()
    mock_is_market_closed.done.side_effect = [False, False, False, True]
    custom_one_second_exit_application_runner.is_market_closed = mock_is_market_closed
    custom_one_second_exit_application_runner.next_onemin_timestamp = pd.Timestamp(
        2025, 3, 27, 15, 28
    )
    mocker.patch(
        "core.runners.one_second_exit_application_base.get_local_timestamp",
        return_value=pd.Timestamp(2025, 3, 27, 15, 29),
    )
    await (
        custom_one_second_exit_application_runner.fetch_data_and_check_exit_conditions()
    )
    assert (
        77
        in custom_one_second_exit_application_runner.position_manager.trade_ids_of_dead_trades
    )
    assert (
        77
        not in custom_one_second_exit_application_runner.position_manager.one_second_exit_positions
    )
    assert (
        79
        in custom_one_second_exit_application_runner.position_manager.trade_ids_of_dead_trades
    )
    assert (
        79
        not in custom_one_second_exit_application_runner.position_manager.one_second_exit_positions
    )
