import pytest
from core.helpers.one_second_exit_application_components.kafka_trade_processor import (
    KafkaTradeProcessor,
)
from unittest.mock import MagicMock, AsyncMock, Mock
from core.runners.one_second_exit_application_base import (
    OMSOneSecondExitApplicationRunnerBase,
)
from core.helpers.one_second_exit_application_components.position_manager import (
    OneSecondExitApplicationPositionManager,
)
import os
import logging
from aiokafka import AIOKafkaConsumer  # type: ignore
import datetime
from core.helpers.configstore import (
    EXECUTION_LEVEL,
    EXCHANGE_TYPE,
)
from fixtures.fixture_classes import (
    MockAIOKafkaConsumer,
    ExitPositionOperation,
    MoveLiveToDeadOperation,
    OMSDBCopyRunner,
    TradingCheckDagBuilder,
    StratWiseTimeDeltaBuilder,
    PositionMismatchDagBuilder,
    OlderTradeAlertDagBuilder,
    BalteTradingDagBuilder,
)


@pytest.fixture
def custom_one_second_exit_application_runner(
    mocker,
) -> OMSOneSecondExitApplicationRunnerBase:
    """
    Creates and returns a test instance of `OMSOneSecondExitApplicationRunnerBase`
    with mocked dependencies.

    This fixture performs the following:
    - Creates necessary log directory (`/opt/balte_live/log/one_second_exit_application`).
    - Instantiates `OMSOneSecondExitApplicationRunnerBase`.
    - Mocks `KafkaTradeProcessor` to prevent actual Kafka interactions.
    - Mocks `data_client.fetch_data` to return predefined last traded price data.
    - Assigns a mocked `toti_obj` to the runner.

    Args:
        mocker: A pytest-mock object used for patching dependencies.

    Returns:
        OMSOneSecondExitApplicationRunnerBase: A test instance of the application runner.
    """
    os.makedirs("/opt/balte_live/log/one_second_exit_application", exist_ok=True)
    one_second_application: OMSOneSecondExitApplicationRunnerBase = (
        OMSOneSecondExitApplicationRunnerBase()
    )
    mock_kafka_trade_processor = MagicMock(spec=KafkaTradeProcessor)
    mock_kafka_trade_processor.read_from_kafka_and_process = AsyncMock(
        return_value=None
    )
    mock_kafka_trade_processor.is_market_closed = MagicMock()
    mocker.patch.object(
        one_second_application,
        "kafka_trade_processor",
        mock_kafka_trade_processor,
    )
    mock_last_traded_price_data = {
        225002025032705001: 200.5,
        226002025032705001: 200.75,
    }
    mocker.patch.object(
        one_second_application.data_client,
        "fetch_data",
        return_value=mock_last_traded_price_data,
    )
    mock_toti_obj = MagicMock()
    one_second_application.toti_obj = mock_toti_obj
    return one_second_application


@pytest.fixture
def custom_kafka_processor(mocker) -> KafkaTradeProcessor:
    """Fixture to provide a KafkaTradeProcessor instance backed by a fake kafka server.

    This fixture uses mockafka.aiokafka to mock kafka functionality,
    enabling tests to run without requiring a live kafka server. The mock
    ensures that any calls to `AIOKafkaConsumer` in the KafkaTradeProcessor
    implementation are redirected to the fake kafka instance.

    Args:
        mocker: A pytest-mock object used for patching the kafka client.

    Returns:
        KafkaTradeProcessor: A KafkaTradeProcessor client instance configured to use the fake kafka server.
    """
    fake_consumer: MockAIOKafkaConsumer = MockAIOKafkaConsumer()
    position_manager: OneSecondExitApplicationPositionManager = (
        OneSecondExitApplicationPositionManager(logger=logging.getLogger())
    )
    kafka_trade_processor: KafkaTradeProcessor = KafkaTradeProcessor(
        logger=logging.getLogger(), position_manager=position_manager
    )
    mocker.patch(
        "core.helpers.one_second_exit_application_components.kafka_trade_processor.AIOKafkaConsumer",
        return_value=fake_consumer,
    )
    mock_is_market_closed = MagicMock()
    mock_is_market_closed.done.side_effect = [False, False, False, True]
    kafka_trade_processor.is_market_closed = mock_is_market_closed

    return kafka_trade_processor


@pytest.fixture
def mock_kafka_consumer() -> AsyncMock:
    """
    A pytest fixture that provides a mocked AIOKafkaConsumer instance
    with predefined log messages to simulate Kafka message consumption.

    This fixture:
    - Creates a mocked `AIOKafkaConsumer` using `AsyncMock`.
    - Populates it with log messages of different log levels (`INFO`, `ERROR`).
    - Includes messages related to trade execution and order rejections.
    - Simulates an async iterator (`__aiter__`) to return predefined messages when consumed.

    Returns:
        AsyncMock: A mocked instance of `AIOKafkaConsumer` with predefined log messages.

    Notes:
        - The log messages simulate different timestamps and log levels.
        - Messages contain placeholders like `{EXCHANGE_TYPE}` and `{EXECUTION_LEVEL}`,
          which should be defined in the test environment.
    """
    current_time = datetime.datetime.now()
    time_str = "09:00"
    message_list = []
    mock_consumer = AsyncMock(spec=AIOKafkaConsumer)
    mock_message = Mock()
    mock_message.value.decode.return_value = f"\n{current_time.strftime('%Y-%m-%d %H:%M:%S')} INFO {EXCHANGE_TYPE}_{EXECUTION_LEVEL}_SLAVEModule:EngineLive Called RunFullLive END {time_str}\n"
    message_list.append(mock_message)

    mock_message = Mock()
    current_time = datetime.datetime.now()
    mock_message.value.decode.return_value = f"\n{current_time.strftime('%Y-%m-%d %H:%M:%S')} INFO krx_production_CLUSTERModule:Order RejectedEntryTradeFromOMS for strategy: cluster_korea, balte_id: 37252024051318001 returning None"
    message_list.append(mock_message)

    mock_message = Mock()
    current_time = datetime.datetime.now()
    mock_message.value.decode.return_value = f"\n{current_time.strftime('%Y-%m-%d %H:%M:%S')} ERROR krx_production_CLUSTERModule:Order RejectedEntryTradeFromOMS for strategy: cluster_korea, balte_id: 37252024051308001 returning None"
    message_list.append(mock_message)

    mock_message = Mock()
    old_time = datetime.datetime.now() - datetime.timedelta(minutes=10)
    mock_message.value.decode.return_value = f"\n{old_time.strftime('%Y-%m-%d %H:%M:%S')} INFO {EXCHANGE_TYPE}_{EXECUTION_LEVEL}_SLAVEModule:EngineLive Called RunFullLive END {time_str}\n"
    message_list.append(mock_message)

    mock_consumer.__aiter__.return_value = message_list

    return mock_consumer


@pytest.fixture
def exit_position_operation() -> ExitPositionOperation:
    """
    A pytest fixture that provides an instance of `ExitPositionOperation`
    initialized with test parameters.

    This fixture:
    - Creates an instance of `ExitPositionOperation` with a `None` toti_obj.
    - Sets the `username` parameter to `"test"` for testing purposes.

    Returns:
        ExitPositionOperation: A configured instance of `ExitPositionOperation`
        ready for use in tests.
    """
    params = {
        "toti_obj": None,
        "username": "test",
    }
    return ExitPositionOperation(**params)


@pytest.fixture
def move_live_to_dead_operation() -> MoveLiveToDeadOperation:
    """
    A pytest fixture that provides an instance of `MoveLiveToDeadOperation`
    initialized with test parameters.

    This fixture:
    - Creates an instance of `MoveLiveToDeadOperation` with the `username` set to `"test"`.

    Returns:
        MoveLiveToDeadOperation: A configured instance of `MoveLiveToDeadOperation`
        ready for use in tests.
    """
    params = {
        "username": "test",
    }
    return MoveLiveToDeadOperation(**params)


@pytest.fixture
def oms_db_copy_runner() -> OMSDBCopyRunner:
    """
    A pytest fixture that provides an instance of `OMSDBCopyRunner`.

    This fixture:
    - Creates and returns an instance of `OMSDBCopyRunner`, which may be used
      in tests that require a database copy runner.

    Returns:
        OMSDBCopyRunner: A new instance of `OMSDBCopyRunner`.
    """
    return OMSDBCopyRunner()


@pytest.fixture
def trading_check_dag_builder() -> TradingCheckDagBuilder:
    """
    A pytest fixture that provides an instance of `TradingCheckDagBuilder`.

    This fixture:
    - Initializes `TradingCheckDagBuilder` with predefined parameters related to trading checks.
    - Returns the instance for use in test cases requiring a trading DAG.

    Returns:
        TradingCheckDagBuilder: A configured instance of `TradingCheckDagBuilder`.
    """
    params = {
        "dag_id": "create_strat_wise_time_delta",
        "schedule_interval": "40 15 * * *",
        "trading_check_end_time": datetime.time(15, 20),
        "no_trade_check_segments": ["OPTIDX", "OPTSTK", "FUTSTK"],
        "no_trade_threshold_min": 15,
        "pickle_state_check_strats": [
            "master_cluster",
            "cluster_options_2024",
            "cluster_callbuy",
            "cluster_putbuy",
            "cluster_kusadasi_2024",
        ],
        "pickle_state_check_threshold_sec": 500,
    }
    return TradingCheckDagBuilder(**params)


@pytest.fixture
def strat_wise_time_delta_dag_builder() -> StratWiseTimeDeltaBuilder:
    """
    A pytest fixture that provides an instance of `StratWiseTimeDeltaBuilder`.

    This fixture initializes `StratWiseTimeDeltaBuilder` with predefined parameters
    related to strategy-wise time delta DAGs.

    Returns:
        StratWiseTimeDeltaBuilder: A configured instance of `StratWiseTimeDeltaBuilder`.
    """
    params = {
        "dag_id": "create_strat_wise_time_delta",
        "schedule_interval": "40 15 * * *",
    }
    return StratWiseTimeDeltaBuilder(**params)


@pytest.fixture
def position_mismatch_dag_builder() -> PositionMismatchDagBuilder:
    """
    A pytest fixture that provides an instance of `PositionMismatchDagBuilder`.

    This fixture initializes `PositionMismatchDagBuilder` with predefined parameters
    for monitoring position mismatches in trading systems.

    Returns:
        PositionMismatchDagBuilder: A configured instance of `PositionMismatchDagBuilder`.
    """
    params = {
        "dag_id": "position_mismatch_monitoring",
        "schedule_interval": "30 18 * * 1-5",
    }
    return PositionMismatchDagBuilder(**params)


@pytest.fixture
def older_trade_alert_dag_builder() -> OlderTradeAlertDagBuilder:
    """
    A pytest fixture that provides an instance of `OlderTradeAlertDagBuilder`.

    This fixture initializes an `OlderTradeAlertDagBuilder` with predefined parameters
    to monitor and alert on older trades.

    Returns:
        OlderTradeAlertDagBuilder: A configured instance of `OlderTradeAlertDagBuilder`.
    """
    params = {"dag_id": "older_trade_alert", "schedule_interval": "00 16 * * 1-5"}
    return OlderTradeAlertDagBuilder(**params)


@pytest.fixture
def balte_trading_dag_builder() -> BalteTradingDagBuilder:
    """
    A pytest fixture that provides an instance of `BalteTradingDagBuilder`.

    This fixture initializes a `BalteTradingDagBuilder` with predefined parameters
    to manage BaLTE trading infrastructure.

    Returns:
        BalteTradingDagBuilder: A configured instance of `BalteTradingDagBuilder`.
    """
    params = {
        "dag_id": "BaLTE_Trading_Infra",
        "schedule_interval": "55 4 * * 1-5",
    }
    return BalteTradingDagBuilder(**params)
