# type: ignore
import pytest
import pandas as pd
from test_data.datastore import SAMPLE_LIVE_TRADES
from core.helpers.configstore import (
    EXCHANGE_TYPE,
    EXECUTION_LEVEL,
)

TABLE_SETUP_PARAM = {
    "name": [
        f"{EXCHANGE_TYPE}_{EXECUTION_LEVEL}",
        f"dead_trade_{EXCHANGE_TYPE}_{EXECUTION_LEVEL}",
    ],
    "value": [pd.DataFrame(SAMPLE_LIVE_TRADES), pd.DataFrame()],
}


@pytest.mark.parametrize("table_setup", [TABLE_SETUP_PARAM], indirect=True)
def test_move_live_to_dead_strat(table_setup, move_live_to_dead_operation):
    move_live_to_dead_operation.move_live_to_dead(
        strat="BaLTE_test_slave_strat", trade_id_list=None
    )

    assert len(move_live_to_dead_operation.get_all_live_trades()) == 1
    assert len(move_live_to_dead_operation.get_all_dead_trades()) == 1


@pytest.mark.parametrize("table_setup", [TABLE_SETUP_PARAM], indirect=True)
def test_move_live_to_dead_trade_ids(table_setup, move_live_to_dead_operation):
    move_live_to_dead_operation.move_live_to_dead(trade_id_list=["2"], strat=None)

    assert len(move_live_to_dead_operation.get_all_live_trades()) == 1
    assert len(move_live_to_dead_operation.get_all_dead_trades()) == 1
