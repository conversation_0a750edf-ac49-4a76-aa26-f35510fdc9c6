from core.helpers.one_second_exit_application_components.position_manager import (
    OneSecondExitApplicationPositionManager,
)
import logging
from core.helpers.one_second_exit_application_components.structs import (
    OneSecondExitPosition,
    OrderStopLossInformation,
)
from balte.struct import OrderStopLossInfo
import pandas as pd
from test_data.datastore import DEAD_TRADES_TO_INSERT, LIVE_TRADES_TO_INSERT
import pytest
import datetime


@pytest.mark.parametrize(
    "table_setup",
    [
        {
            "name": ["dead_trade_nse_test", "nse_test"],
            "value": [
                pd.DataFrame(DEAD_TRADES_TO_INSERT),
                pd.DataFrame(LIVE_TRADES_TO_INSERT),
            ],
        }
    ],
    indirect=True,
)
def test_position_manager(table_setup):
    position_manager: OneSecondExitApplicationPositionManager = (
        OneSecondExitApplicationPositionManager(logger=logging.getLogger())
    )
    assert set([73, 75]) == position_manager.trade_ids_of_dead_trades
    assert len(position_manager.one_second_exit_positions) == 2
    assert len(position_manager.fixed_timestamp_exit_mapping) == 2
    assert position_manager.fixed_timestamp_exit_mapping[
        datetime.datetime(2025, 3, 25, 15, 24)
    ] == [77]
    assert position_manager.fixed_timestamp_exit_mapping[
        datetime.datetime(2025, 3, 25, 15, 29)
    ] == [79]
    assert position_manager.one_second_exit_positions[77] == OneSecondExitPosition(
        TRADEID=77,
        SYMBOL="NIFTY",
        QUANTITY=75,
        STRATEGY="BaLTE_ravat_jordan_bn_month",
        ENTRY_TIMESTAMP=pd.Timestamp("2025-03-25 15:15:04"),
        SEGMENT="OPTIDX",
        EXPIRY="27-Mar-2025",
        TYPE="PE",
        STRIKE=22500.0,
        ENTRY_PRICE=16.575,
        ORDER_TYPE="NORMAL",
        ENTRY_TIMESTAMP_META=pd.Timestamp("2025-03-25 15:15:00"),
        SLAVE_NAME="default_slave",
        EXTENDED_INFO='{"hedge_of_order_id": 76, "order_stoploss_info": [{"universe": "opt_onemin", "balte_id": 225002025032705001, "price": 198.75, "operator": ">="}, {"fixed_exit_timestamp": "2025-03-25T15:24:00"}], "serialized_meta_obj": ""}',
    )
    assert position_manager.active_balte_id_count_mapping == {
        225002025032705001: 1,
        226002025032705001: 1,
        5001: 1,
    }
    assert position_manager.one_sec_stoploss_info_list == [
        OrderStopLossInformation(
            trade_id=77,
            order_stoploss_info=OrderStopLossInfo(
                universe="opt_onemin",
                balte_id=225002025032705001,
                price=198.75,
                operator=">=",
                exit_on_1min=False,
            ),
        ),
        OrderStopLossInformation(
            trade_id=79,
            order_stoploss_info=OrderStopLossInfo(
                universe="opt_onemin",
                balte_id=226002025032705001,
                price=218.75,
                operator=">=",
                exit_on_1min=False,
            ),
        ),
    ]
    assert position_manager.one_min_stoploss_info_list == [
        OrderStopLossInformation(
            trade_id=79,
            order_stoploss_info=OrderStopLossInfo(
                universe="futidx_onemin",
                balte_id=5001,
                price=22634.75,
                operator=">=",
                exit_on_1min=True,
            ),
        )
    ]
