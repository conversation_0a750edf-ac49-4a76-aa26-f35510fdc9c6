# type: ignore
import pytest
from typing import List


@pytest.mark.parametrize(
    "task_count, task_ids",
    [
        (
            8,
            [
                "trading_holiday_check",
                "trading_day",
                "holiday",
                "dead_strat_live_trades_checker",
                "cluster_slave_trade_qty_checker",
                "pickle_state_checker",
                "segment_wise_trade_checker",
                "eod_pickle_checker",
            ],
        )
    ],
)
def test_dag_structure(trading_check_dag_builder, task_count: int, task_ids: List[str]):
    # check for import errors by building dag here
    dag = trading_check_dag_builder.build_dag()
    assert dag.dag_id == "create_strat_wise_time_delta"

    assert (
        len(dag.tasks) == task_count
    ), f"Expected {task_count} tasks in DAG '{dag.dag_id}', found {len(dag.tasks)}"

    for task_id in task_ids:
        assert task_id in dag.task_dict, f"Task with id: {task_id} not present in dag"

    assert len(dag.task_dict["trading_day"].downstream_task_ids) == 4
    tasks = [
        "dead_strat_live_trades_checker",
        "cluster_slave_trade_qty_checker",
        "pickle_state_checker",
        "segment_wise_trade_checker",
    ]
    for task in tasks:
        assert task in dag.task_dict["trading_day"].downstream_task_ids
    assert (
        "eod_pickle_checker"
        in dag.task_dict["pickle_state_checker"].downstream_task_ids
    )
