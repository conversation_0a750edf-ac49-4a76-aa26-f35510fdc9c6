# type: ignore
import pytest
import pandas as pd
from typing import List


@pytest.mark.parametrize(
    "log_file_path, is_slow_strat_data",
    [
        ("/opt/test_data/slave_engine.log", False),
        ("/opt/test_data/slave_engine_slow.log", True),
    ],
)
def test_get_strat_df(
    monkeypatch,
    strat_wise_time_delta_dag_builder,
    log_file_path: str,
    is_slow_strat_data: bool,
):
    def mock_slave_log_file_paths(*args, **kwargs) -> List[str]:
        return [log_file_path]

    monkeypatch.setattr(
        "core.dags.monitoring.strat_wise_time_delta_builder_base.StratWiseTimeDeltaBuilderBase.get_slave_log_file_paths",
        mock_slave_log_file_paths,
    )

    def mock_get_local_date(*args, **kwargs) -> pd.Timestamp:
        return pd.Timestamp("2024-09-19").normalize()

    monkeypatch.setattr(
        "core.dags.monitoring.strat_wise_time_delta_builder_base.get_local_date",
        mock_get_local_date,
    )

    def mock_send_email(sub: str, txt: str, recipients: List[str], msgtype: str):
        if not is_slow_strat_data:
            assert pd.read_html(txt)[0].empty
        else:
            df = pd.read_html(txt)[0]
            assert len(df) == 1
            assert df["strategy"][0] == "achim"
            assert df["tot_sec"][0] == 5.004

    monkeypatch.setattr(
        "core.dags.monitoring.strat_wise_time_delta_builder_base.send_email",
        mock_send_email,
    )
    strat_wise_time_delta_dag_builder.get_strat_df()


@pytest.mark.parametrize(
    "task_count, task_ids",
    [
        (
            5,
            [
                "trading_holiday_check",
                "trading_day",
                "holiday",
                "create_timedelta_and_email",
                "failure_email",
            ],
        )
    ],
)
def test_dag_structure(
    strat_wise_time_delta_dag_builder,
    task_count: int,
    task_ids: List[str],
):
    # check for import errors by building dag here
    dag = strat_wise_time_delta_dag_builder.build_dag()
    assert dag.dag_id == "create_strat_wise_time_delta"

    assert (
        len(dag.tasks) == task_count
    ), f"Expected {task_count} tasks in DAG '{dag.dag_id}', found {len(dag.tasks)}"

    for task_id in task_ids:
        assert task_id in dag.task_dict, f"Task with id: {task_id} not present in dag"

    assert len(dag.task_dict["create_timedelta_and_email"].upstream_task_ids) == 1
    assert len(dag.task_dict["create_timedelta_and_email"].downstream_task_ids) == 1
    assert (
        "trading_day" in dag.task_dict["create_timedelta_and_email"].upstream_task_ids
    )
    assert (
        "failure_email"
        in dag.task_dict["create_timedelta_and_email"].downstream_task_ids
    )
