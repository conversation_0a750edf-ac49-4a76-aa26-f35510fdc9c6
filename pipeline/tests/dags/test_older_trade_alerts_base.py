# type: ignore
import pytest
import pandas as pd
from typing import List
from test_data.datastore import SAMPLE_LIVE_TRADES
from core.helpers.configstore import (
    EXCHANGE_TYPE,
    EXECUTION_LEVEL,
)

TABLE_SETUP_PARAM = {
    "name": [f"{EXCHANGE_TYPE}_{EXECUTION_LEVEL}"],
    "value": [pd.DataFrame(SAMPLE_LIVE_TRADES)],
}


@pytest.mark.parametrize(
    "is_empty_trade_data, table_setup",
    [(False, TABLE_SETUP_PARAM), (True, TABLE_SETUP_PARAM)],
    indirect=["table_setup"],
)
def test_check_older_trades(
    monkeypatch, table_setup, older_trade_alert_dag_builder, is_empty_trade_data: bool
):
    def mock_get_local_date(*args, **kwargs):
        if is_empty_trade_data:
            return pd.Timestamp("Dec 25, 2023, 9:30 AM").normalize()
        else:
            return pd.Timestamp("Jan 19, 2024, 9:30 AM").normalize()

    monkeypatch.setattr(
        "core.dags.monitoring.older_trade_alert_builder_base.get_local_date",
        mock_get_local_date,
    )
    if not is_empty_trade_data:
        with pytest.raises(ValueError, match="Older Trades Found") as exc_info:
            older_trade_alert_dag_builder.check_older_trades()

        assert exc_info.type is ValueError
        assert exc_info.value.args[0] == "Older Trades Found"
    else:
        assert older_trade_alert_dag_builder.check_older_trades() is None


@pytest.mark.parametrize(
    "task_count, task_ids",
    [(1, ["check_oldertrades"])],
)
def test_dag_structure(
    older_trade_alert_dag_builder, task_count: int, task_ids: List[str]
):
    # check for import errors by building dag here
    dag = older_trade_alert_dag_builder.build_dag()
    assert dag.dag_id == "older_trade_alert"

    assert (
        len(dag.tasks) == task_count
    ), f"Expected {task_count} tasks in DAG '{dag.dag_id}', found {len(dag.tasks)}"

    for task_id in task_ids:
        assert task_id in dag.task_dict, f"Task with id: {task_id} not present in dag"
