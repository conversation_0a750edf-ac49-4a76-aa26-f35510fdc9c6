# type: ignore
import pytest
from typing import List


@pytest.mark.parametrize(
    "task_count, task_ids",
    [
        (
            4,
            [
                "trading_holiday_check",
                "trading_day",
                "holiday",
                "position_mismatch_check",
            ],
        )
    ],
)
def test_dag_structure(
    position_mismatch_dag_builder, task_count: int, task_ids: List[str]
):
    # check for import errors by building dag here
    dag = position_mismatch_dag_builder.build_dag()
    assert dag.dag_id == "position_mismatch_monitoring"

    assert (
        len(dag.tasks) == task_count
    ), f"Expected {task_count} tasks in DAG '{dag.dag_id}', found {len(dag.tasks)}"

    for task_id in task_ids:
        assert task_id in dag.task_dict, f"Task with id: {task_id} not present in dag"

    assert len(dag.task_dict["position_mismatch_check"].upstream_task_ids) == 1
    assert len(dag.task_dict["position_mismatch_check"].downstream_task_ids) == 0
    assert "trading_day" in dag.task_dict["position_mismatch_check"].upstream_task_ids

    assert len(dag.task_dict["holiday"].downstream_task_ids) == 0
